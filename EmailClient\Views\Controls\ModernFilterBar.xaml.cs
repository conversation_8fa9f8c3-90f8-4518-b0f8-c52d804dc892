using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views.Controls;

public partial class ModernFilterBar : UserControl, INotifyPropertyChanged
{
    public static readonly DependencyProperty SelectedFilterProperty =
        DependencyProperty.Register("SelectedFilter", typeof(string), 
            typeof(ModernFilterBar), new PropertyMetadata("All", OnSelectedFilterChanged));

    public static readonly DependencyProperty FilterCountProperty =
        DependencyProperty.Register("FilterCount", typeof(int), 
            typeof(ModernFilterBar), new PropertyMetadata(0, OnFilterCountChanged));

    public static readonly RoutedEvent FilterChangedEvent = 
        EventManager.RegisterRoutedEvent("FilterChanged", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernFilterBar));

    public event PropertyChangedEventHandler? PropertyChanged;
    public event RoutedEventHandler FilterChanged
    {
        add { AddHandler(FilterChangedEvent, value); }
        remove { RemoveHandler(FilterChangedEvent, value); }
    }

    public string SelectedFilter
    {
        get { return (string)GetValue(SelectedFilterProperty); }
        set { SetValue(SelectedFilterProperty, value); }
    }

    public int FilterCount
    {
        get { return (int)GetValue(FilterCountProperty); }
        set { SetValue(FilterCountProperty, value); }
    }

    public ModernFilterBar()
    {
        InitializeComponent();
        DataContext = this;
        UpdateFilterButtons();
    }

    private static void OnSelectedFilterChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernFilterBar control)
        {
            control.UpdateFilterButtons();
            control.OnPropertyChanged(nameof(SelectedFilter));
        }
    }

    private static void OnFilterCountChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernFilterBar control)
        {
            control.UpdateFilterCountDisplay();
            control.OnPropertyChanged(nameof(FilterCount));
        }
    }

    private void FilterButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string filter)
        {
            SelectedFilter = filter;
            
            var args = new FilterChangedEventArgs(FilterChangedEvent, this)
            {
                SelectedFilter = filter
            };
            RaiseEvent(args);
        }
    }

    private void UpdateFilterButtons()
    {
        // Reset all buttons to secondary style
        AllFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");
        UnreadFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");
        ImportantFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");
        AttachmentsFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");

        // Set active button to primary style
        Button? activeButton = SelectedFilter switch
        {
            "All" => AllFilterButton,
            "Unread" => UnreadFilterButton,
            "Important" => ImportantFilterButton,
            "Attachments" => AttachmentsFilterButton,
            _ => AllFilterButton
        };

        if (activeButton != null)
        {
            activeButton.Style = (Style)FindResource("ModernButtonStyle");
        }
    }

    private void UpdateFilterCountDisplay()
    {
        if (FilterCount > 0)
        {
            FilterCountBadge.Visibility = Visibility.Visible;
            FilterCountText.Text = SelectedFilter switch
            {
                "Unread" => $"{FilterCount} Unread",
                "Important" => $"{FilterCount} Important",
                "Attachments" => $"{FilterCount} with Attachments",
                _ => $"{FilterCount} New"
            };
        }
        else
        {
            FilterCountBadge.Visibility = Visibility.Collapsed;
        }
    }

    public void SetFilter(string filter)
    {
        SelectedFilter = filter;
    }

    public void UpdateCount(int count)
    {
        FilterCount = count;
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class FilterChangedEventArgs : RoutedEventArgs
{
    public string SelectedFilter { get; set; } = string.Empty;

    public FilterChangedEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
    {
    }
}
