import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Plus, 
  X, 
  Settings, 
  Brain, 
  Mail, 
  User, 
  Calendar,
  FileText,
  Tag,
  Filter,
  Code,
  Zap
} from "lucide-react";

interface EmailCondition {
  id: string;
  type: "sender" | "subject" | "body" | "attachment" | "intent" | "urgency" | "entities" | "custom";
  operator: "equals" | "contains" | "startsWith" | "endsWith" | "matches" | "greaterThan" | "lessThan" | "in" | "notIn";
  value: string;
  caseSensitive: boolean;
  enabled: boolean;
}

interface ConditionGroup {
  id: string;
  logic: "AND" | "OR";
  conditions: EmailCondition[];
}

interface ConditionBuilderProps {
  onConditionsChange: (groups: ConditionGroup[]) => void;
  initialConditions?: ConditionGroup[];
}

export default function ConditionBuilder({ onConditionsChange, initialConditions = [] }: ConditionBuilderProps) {
  const [conditionGroups, setConditionGroups] = useState<ConditionGroup[]>(
    initialConditions.length > 0 ? initialConditions : [
      {
        id: "group_1",
        logic: "AND",
        conditions: []
      }
    ]
  );

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [naturalLanguageRule, setNaturalLanguageRule] = useState("");

  const conditionTypes = [
    { value: "sender", label: "Sender Email", icon: User },
    { value: "subject", label: "Subject Line", icon: Mail },
    { value: "body", label: "Email Body", icon: FileText },
    { value: "attachment", label: "Has Attachment", icon: FileText },
    { value: "intent", label: "Email Intent", icon: Brain },
    { value: "urgency", label: "Urgency Level", icon: Zap },
    { value: "entities", label: "Named Entities", icon: Tag },
    { value: "custom", label: "Custom Rule", icon: Code }
  ];

  const operators = [
    { value: "equals", label: "Equals", types: ["sender", "subject", "body", "intent", "urgency"] },
    { value: "contains", label: "Contains", types: ["sender", "subject", "body", "entities"] },
    { value: "startsWith", label: "Starts with", types: ["sender", "subject", "body"] },
    { value: "endsWith", label: "Ends with", types: ["sender", "subject", "body"] },
    { value: "matches", label: "Matches regex", types: ["sender", "subject", "body", "custom"] },
    { value: "in", label: "Is one of", types: ["sender", "intent", "urgency", "entities"] },
    { value: "notIn", label: "Is not one of", types: ["sender", "intent", "urgency", "entities"] }
  ];

  const addConditionGroup = () => {
    const newGroup: ConditionGroup = {
      id: `group_${Date.now()}`,
      logic: "AND",
      conditions: []
    };
    setConditionGroups([...conditionGroups, newGroup]);
    onConditionsChange([...conditionGroups, newGroup]);
  };

  const removeConditionGroup = (groupId: string) => {
    const updated = conditionGroups.filter(group => group.id !== groupId);
    setConditionGroups(updated);
    onConditionsChange(updated);
  };

  const addCondition = (groupId: string) => {
    const newCondition: EmailCondition = {
      id: `condition_${Date.now()}`,
      type: "sender",
      operator: "equals",
      value: "",
      caseSensitive: false,
      enabled: true
    };

    const updated = conditionGroups.map(group => 
      group.id === groupId 
        ? { ...group, conditions: [...group.conditions, newCondition] }
        : group
    );
    setConditionGroups(updated);
    onConditionsChange(updated);
  };

  const removeCondition = (groupId: string, conditionId: string) => {
    const updated = conditionGroups.map(group => 
      group.id === groupId 
        ? { ...group, conditions: group.conditions.filter(c => c.id !== conditionId) }
        : group
    );
    setConditionGroups(updated);
    onConditionsChange(updated);
  };

  const updateCondition = (groupId: string, conditionId: string, field: keyof EmailCondition, value: any) => {
    const updated = conditionGroups.map(group => 
      group.id === groupId 
        ? { 
            ...group, 
            conditions: group.conditions.map(c => 
              c.id === conditionId ? { ...c, [field]: value } : c
            )
          }
        : group
    );
    setConditionGroups(updated);
    onConditionsChange(updated);
  };

  const updateGroupLogic = (groupId: string, logic: "AND" | "OR") => {
    const updated = conditionGroups.map(group => 
      group.id === groupId ? { ...group, logic } : group
    );
    setConditionGroups(updated);
    onConditionsChange(updated);
  };

  const parseNaturalLanguage = () => {
    if (!naturalLanguageRule.trim()) return;

    // Simple natural language parsing - in production this would use NLP
    const rule = naturalLanguageRule.toLowerCase();
    const newConditions: EmailCondition[] = [];

    // Parse common patterns
    if (rule.includes("from") && rule.includes("@")) {
      const emailMatch = rule.match(/from\s+([^\s]+@[^\s]+)/);
      if (emailMatch) {
        newConditions.push({
          id: `condition_${Date.now()}`,
          type: "sender",
          operator: "equals",
          value: emailMatch[1],
          caseSensitive: false,
          enabled: true
        });
      }
    }

    if (rule.includes("subject") && rule.includes("contains")) {
      const subjectMatch = rule.match(/subject\s+contains\s+"([^"]+)"/);
      if (subjectMatch) {
        newConditions.push({
          id: `condition_${Date.now()}_1`,
          type: "subject",
          operator: "contains",
          value: subjectMatch[1],
          caseSensitive: false,
          enabled: true
        });
      }
    }

    if (rule.includes("urgent") || rule.includes("high priority")) {
      newConditions.push({
        id: `condition_${Date.now()}_2`,
        type: "urgency",
        operator: "equals",
        value: "high",
        caseSensitive: false,
        enabled: true
      });
    }

    if (newConditions.length > 0) {
      const newGroup: ConditionGroup = {
        id: `group_${Date.now()}`,
        logic: "AND",
        conditions: newConditions
      };
      setConditionGroups([...conditionGroups, newGroup]);
      onConditionsChange([...conditionGroups, newGroup]);
      setNaturalLanguageRule("");
    }
  };

  const getValidOperators = (type: string) => {
    return operators.filter(op => op.types.includes(type));
  };

  const getConditionIcon = (type: string) => {
    const typeConfig = conditionTypes.find(t => t.value === type);
    return typeConfig ? typeConfig.icon : Mail;
  };

  const getExampleValue = (type: string) => {
    switch (type) {
      case "sender": return "<EMAIL>";
      case "subject": return "Invoice";
      case "body": return "meeting request";
      case "intent": return "request_meeting";
      case "urgency": return "high";
      case "entities": return "John Doe";
      case "custom": return "^[A-Z]+$";
      default: return "";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            Smart Email Conditions
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Brain className="w-4 h-4 mr-2" />
                  Natural Language
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Natural Language Rule Builder</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="natural-rule">Describe your rule in plain English:</Label>
                    <Textarea
                      id="natural-rule"
                      placeholder='e.g., "From <EMAIL> and subject contains urgent"'
                      value={naturalLanguageRule}
                      onChange={(e) => setNaturalLanguageRule(e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg text-sm">
                    <strong>Examples:</strong>
                    <ul className="mt-1 space-y-1">
                      <li>• "From <EMAIL> and subject contains invoice"</li>
                      <li>• "Body contains meeting request and high priority"</li>
                      <li>• "From any @client.com email with urgent in subject"</li>
                    </ul>
                  </div>
                  <Button onClick={parseNaturalLanguage} className="w-full">
                    <Brain className="w-4 h-4 mr-2" />
                    Parse & Add Conditions
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
            <Button variant="outline" size="sm" onClick={() => setShowAdvanced(!showAdvanced)}>
              <Settings className="w-4 h-4 mr-2" />
              {showAdvanced ? "Simple" : "Advanced"}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {conditionGroups.map((group, groupIndex) => (
          <Card key={group.id} className="border-dashed">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">
                    Group {groupIndex + 1}
                  </Badge>
                  <Select 
                    value={group.logic} 
                    onValueChange={(value: "AND" | "OR") => updateGroupLogic(group.id, value)}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AND">AND</SelectItem>
                      <SelectItem value="OR">OR</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    ({group.logic === "AND" ? "All" : "Any"} conditions must match)
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => addCondition(group.id)}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Condition
                  </Button>
                  {conditionGroups.length > 1 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeConditionGroup(group.id)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              {group.conditions.map((condition, conditionIndex) => {
                const ConditionIcon = getConditionIcon(condition.type);
                return (
                  <div key={condition.id} className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center space-x-2 flex-1">
                      <ConditionIcon className="w-4 h-4 text-gray-500" />
                      
                      <Select
                        value={condition.type}
                        onValueChange={(value) => updateCondition(group.id, condition.id, "type", value)}
                      >
                        <SelectTrigger className="w-36">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {conditionTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={condition.operator}
                        onValueChange={(value) => updateCondition(group.id, condition.id, "operator", value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {getValidOperators(condition.type).map((op) => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Input
                        placeholder={getExampleValue(condition.type)}
                        value={condition.value}
                        onChange={(e) => updateCondition(group.id, condition.id, "value", e.target.value)}
                        className="flex-1"
                      />

                      {showAdvanced && (
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={condition.caseSensitive}
                            onCheckedChange={(checked) => updateCondition(group.id, condition.id, "caseSensitive", checked)}
                          />
                          <Label className="text-xs whitespace-nowrap">Case sensitive</Label>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={condition.enabled}
                        onCheckedChange={(checked) => updateCondition(group.id, condition.id, "enabled", checked)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCondition(group.id, condition.id)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}

              {group.conditions.length === 0 && (
                <div className="text-center py-6 text-gray-500 dark:text-gray-400">
                  <Filter className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No conditions in this group</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addCondition(group.id)}
                    className="mt-2"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add First Condition
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={addConditionGroup}
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Add Condition Group</span>
          </Button>
        </div>

        {conditionGroups.some(group => group.conditions.length > 0) && (
          <Card className="bg-blue-50 dark:bg-blue-900/20">
            <CardContent className="p-4">
              <h4 className="font-medium text-blue-900 dark:text-blue-200 mb-2">
                Rule Summary:
              </h4>
              <div className="text-sm text-blue-800 dark:text-blue-300">
                {conditionGroups.map((group, index) => (
                  <div key={group.id}>
                    {index > 0 && <span className="font-medium"> OR </span>}
                    <span className="font-medium">Group {index + 1}:</span>
                    <span className="ml-2">
                      {group.conditions.filter(c => c.enabled).map((condition, condIndex) => (
                        <span key={condition.id}>
                          {condIndex > 0 && <span className="font-medium"> {group.logic} </span>}
                          {condition.type} {condition.operator} "{condition.value}"
                        </span>
                      ))}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}