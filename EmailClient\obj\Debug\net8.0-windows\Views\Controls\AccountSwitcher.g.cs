﻿#pragma checksum "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B311CD61A43A0C5B8E650E66E8DBB7403B377CE1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EmailClient.Views.Controls {
    
    
    /// <summary>
    /// AccountSwitcher
    /// </summary>
    public partial class AccountSwitcher : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 11 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccountSwitcherButton;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountNameText;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AccountInfoText;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DropdownArrow;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup AccountDropdown;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UnifiedInboxButton;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UnifiedInboxInfo;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl AccountsList;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddAccountButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EmailClient;V1.0.0.0;component/views/controls/accountswitcher.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AccountSwitcherButton = ((System.Windows.Controls.Button)(target));
            
            #line 16 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
            this.AccountSwitcherButton.Click += new System.Windows.RoutedEventHandler(this.AccountSwitcherButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StatusIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.AccountNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AccountInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.DropdownArrow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AccountDropdown = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 7:
            this.UnifiedInboxButton = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
            this.UnifiedInboxButton.Click += new System.Windows.RoutedEventHandler(this.AccountOption_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.UnifiedInboxInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.AccountsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 11:
            this.AddAccountButton = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
            this.AddAccountButton.Click += new System.Windows.RoutedEventHandler(this.AddAccountButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 132 "..\..\..\..\..\Views\Controls\AccountSwitcher.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AccountOption_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

