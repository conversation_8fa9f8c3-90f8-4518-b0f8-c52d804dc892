<UserControl x:Class="EmailClient.Views.Controls.ModernEmailList"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EmailClient.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="400">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <local:InvertedNullToVisibilityConverter x:Key="InvertedNullToVisibilityConverter"/>
        <local:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Border Background="{StaticResource SurfaceBrush}"
            BorderBrush="{StaticResource BorderBrush}" 
            BorderThickness="0,0,1,0">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Filter Bar -->
            <Border Grid.Row="0" 
                    Padding="16,12" 
                    BorderBrush="{StaticResource BorderBrush}" 
                    BorderThickness="0,0,0,1">
                <StackPanel Orientation="Horizontal">
                    <Button x:Name="AllFilterButton"
                            Content="All" 
                            Style="{StaticResource ModernButtonStyle}"
                            Padding="12,6"
                            FontSize="12"
                            Margin="0,0,8,0"
                            Click="FilterButton_Click"
                            Tag="All"/>
                    <Button x:Name="UnreadFilterButton"
                            Content="Unread" 
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            Padding="12,6"
                            FontSize="12"
                            Margin="0,0,8,0"
                            Click="FilterButton_Click"
                            Tag="Unread"/>
                    <Button x:Name="ImportantFilterButton"
                            Content="Important" 
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            Padding="12,6"
                            FontSize="12"
                            Click="FilterButton_Click"
                            Tag="Important"/>
                </StackPanel>
            </Border>
            
            <!-- Email List -->
            <ListView Grid.Row="1"
                      x:Name="EmailListView"
                      ItemsSource="{Binding FilteredEmails}" 
                      SelectedItem="{Binding SelectedMessage}"
                      Style="{StaticResource ModernListViewStyle}"
                      ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <ListView.ItemTemplate>
                    <DataTemplate>
                        <Border Padding="16,12" 
                                Background="Transparent"
                                BorderThickness="0,0,0,1"
                                BorderBrush="{StaticResource BorderLightBrush}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Avatar -->
                                <Border Grid.Column="0" 
                                        Width="40" Height="40" 
                                        Background="{StaticResource BorderBrush}" 
                                        CornerRadius="20"
                                        Margin="0,0,12,0">
                                    <TextBlock Text="{Binding SenderInitial}" 
                                               Style="{StaticResource BodyTextStyle}"
                                               FontWeight="Medium"
                                               HorizontalAlignment="Center" 
                                               VerticalAlignment="Center"/>
                                </Border>
                                
                                <!-- Email Content -->
                                <StackPanel Grid.Column="1">
                                    <Grid Margin="0,0,0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                   Text="{Binding FromDisplay}" 
                                                   Style="{StaticResource BodyTextStyle}"
                                                   FontWeight="{Binding FontWeight}"
                                                   TextTrimming="CharacterEllipsis"/>
                                        
                                        <TextBlock Grid.Column="1" 
                                                   Text="{Binding DateDisplay}" 
                                                   Style="{StaticResource CaptionTextStyle}"
                                                   Margin="8,0,0,0"/>
                                    </Grid>
                                    
                                    <TextBlock Text="{Binding Subject}" 
                                               Style="{StaticResource BodyTextStyle}"
                                               FontWeight="{Binding FontWeight}"
                                               TextTrimming="CharacterEllipsis"
                                               Margin="0,0,0,4"/>
                                    
                                    <TextBlock Text="{Binding Preview}" 
                                               Style="{StaticResource CaptionTextStyle}"
                                               TextTrimming="CharacterEllipsis"
                                               MaxHeight="32"
                                               TextWrapping="Wrap"/>
                                    
                                    <!-- Attachments and Flags -->
                                    <StackPanel Orientation="Horizontal"
                                                Margin="0,4,0,0"
                                                Visibility="{Binding HasAttachments, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <TextBlock Text="{StaticResource Icon.Attachment}"
                                                   Style="{StaticResource ModernIconStyle}"
                                                   FontSize="12"
                                                   Margin="0,0,4,0"
                                                   Foreground="{StaticResource TextMutedBrush}"/>
                                        <TextBlock Text="Attachments"
                                                   Style="{StaticResource CaptionTextStyle}"
                                                   FontSize="11"/>
                                    </StackPanel>
                                </StackPanel>
                                
                                <!-- Status Indicators -->
                                <StackPanel Grid.Column="2" 
                                            Orientation="Vertical" 
                                            VerticalAlignment="Top"
                                            Margin="8,0,0,0">
                                    <!-- Unread Indicator -->
                                    <Border Style="{StaticResource UnreadIndicatorStyle}"
                                            Visibility="{Binding IsRead, Converter={StaticResource InvertedBooleanToVisibilityConverter}}"
                                            Margin="0,0,0,4"/>
                                    
                                    <!-- Star Button -->
                                    <Button Style="{StaticResource ModernIconButtonStyle}"
                                            Width="24" Height="24"
                                            Padding="4"
                                            Click="StarButton_Click"
                                            Tag="{Binding}">
                                        <TextBlock Text="{Binding StarIcon}" FontSize="12"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ListView.ItemTemplate>
                <ListView.ItemContainerStyle>
                    <Style TargetType="ListViewItem" BasedOn="{StaticResource ModernListViewItemStyle}">
                        <Setter Property="Padding" Value="0"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsRead}" Value="False">
                                <Setter Property="Background" Value="{StaticResource UnreadBrush}"/>
                            </DataTrigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource SelectedBrush}"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ListView.ItemContainerStyle>
            </ListView>
            
            <!-- Empty State -->
            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Visibility="{Binding HasEmails, Converter={StaticResource InvertedBooleanToVisibilityConverter}}">
                <TextBlock Text="{StaticResource Icon.Email}"
                           Style="{StaticResource ModernIconStyle}"
                           FontSize="48"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,16"
                           Foreground="{StaticResource TextMutedBrush}"/>
                <TextBlock Text="No emails found"
                           Style="{StaticResource BodyTextStyle}"
                           Foreground="{StaticResource TextMutedBrush}"
                           HorizontalAlignment="Center"/>
                <TextBlock Text="Try adjusting your filters or check back later"
                           Style="{StaticResource CaptionTextStyle}"
                           HorizontalAlignment="Center"
                           Margin="0,4,0,0"/>
            </StackPanel>
            
            <!-- Loading State -->
            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Border Width="32" Height="32"
                        Background="{StaticResource PrimaryBrush}"
                        CornerRadius="16"
                        Margin="0,0,0,16">
                    <TextBlock Text="{StaticResource Icon.Syncing}"
                               Style="{StaticResource ModernIconStyle}"
                               FontSize="16"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </Border>
                <TextBlock Text="Loading emails..."
                           Style="{StaticResource BodyTextStyle}"
                           Foreground="{StaticResource TextMutedBrush}"
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
