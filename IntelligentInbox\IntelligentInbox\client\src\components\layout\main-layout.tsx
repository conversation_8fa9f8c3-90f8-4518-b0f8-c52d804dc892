import { useState } from "react";
import Sidebar from "./sidebar";
import TaskPanel from "@/components/tasks/task-panel";
import { useWebSocket } from "@/hooks/use-websocket";

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const [showTaskPanel, setShowTaskPanel] = useState(false);
  
  // Initialize WebSocket connection (disabled in development)
  useWebSocket();

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      <Sidebar />
      
      <div className="flex-1 flex overflow-hidden">
        <main className="flex-1 flex flex-col">
          {children}
        </main>
      </div>
      
      {showTaskPanel && (
        <TaskPanel onClose={() => setShowTaskPanel(false)} />
      )}
    </div>
  );
}
