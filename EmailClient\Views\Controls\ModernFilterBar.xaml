<UserControl x:Class="EmailClient.Views.Controls.ModernFilterBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="400">
    
    <StackPanel Orientation="Horizontal">
        <!-- Filter Buttons -->
        <Button x:Name="AllFilterButton"
                Content="All" 
                Style="{StaticResource ModernButtonStyle}"
                Padding="12,6"
                FontSize="12"
                Margin="0,0,8,0"
                Click="FilterButton_Click"
                Tag="All"/>
        
        <Button x:Name="UnreadFilterButton"
                Content="Unread" 
                Style="{StaticResource ModernSecondaryButtonStyle}"
                Padding="12,6"
                FontSize="12"
                Margin="0,0,8,0"
                Click="FilterButton_Click"
                Tag="Unread">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <StackPanel Orientation="Horizontal" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                            <Border x:Name="UnreadIndicator"
                                    Width="6" Height="6" 
                                    Background="{StaticResource PrimaryBrush}" 
                                    CornerRadius="3"
                                    Margin="0,0,6,0"
                                    VerticalAlignment="Center"/>
                            <ContentPresenter/>
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Button.Template>
        </Button>
        
        <Button x:Name="ImportantFilterButton"
                Content="Important" 
                Style="{StaticResource ModernSecondaryButtonStyle}"
                Padding="12,6"
                FontSize="12"
                Margin="0,0,8,0"
                Click="FilterButton_Click"
                Tag="Important">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <StackPanel Orientation="Horizontal" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                            <TextBlock Text="⭐" 
                                       FontSize="10" 
                                       Margin="0,0,6,0"
                                       VerticalAlignment="Center"/>
                            <ContentPresenter/>
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Button.Template>
        </Button>
        
        <Button x:Name="AttachmentsFilterButton"
                Content="Attachments" 
                Style="{StaticResource ModernSecondaryButtonStyle}"
                Padding="12,6"
                FontSize="12"
                Margin="0,0,8,0"
                Click="FilterButton_Click"
                Tag="Attachments">
            <Button.Template>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <StackPanel Orientation="Horizontal" 
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                            <TextBlock Text="📎" 
                                       FontSize="10" 
                                       Margin="0,0,6,0"
                                       VerticalAlignment="Center"/>
                            <ContentPresenter/>
                        </StackPanel>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Button.Template>
        </Button>
        
        <!-- Filter Count Badge -->
        <Border x:Name="FilterCountBadge"
                Background="{StaticResource PrimaryBrush}" 
                CornerRadius="12" 
                Padding="8,4"
                Margin="8,0,0,0"
                VerticalAlignment="Center"
                Visibility="Collapsed">
            <TextBlock x:Name="FilterCountText"
                       Text="24 New" 
                       Foreground="White" 
                       FontSize="12" 
                       FontWeight="Medium"/>
        </Border>
    </StackPanel>
</UserControl>
