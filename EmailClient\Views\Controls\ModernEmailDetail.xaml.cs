using EmailClient.ViewModels;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views.Controls;

public partial class ModernEmailDetail : UserControl, INotifyPropertyChanged
{
    public static readonly DependencyProperty SelectedEmailProperty =
        DependencyProperty.Register("SelectedEmail", typeof(EmailMessageViewModel), 
            typeof(ModernEmailDetail), new PropertyMetadata(null, OnSelectedEmailChanged));

    public static readonly RoutedEvent EmailActionEvent = 
        EventManager.RegisterRoutedEvent("EmailAction", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernEmailDetail));

    public event PropertyChangedEventHandler? PropertyChanged;
    public event RoutedEventHandler EmailAction
    {
        add { AddHandler(EmailActionEvent, value); }
        remove { RemoveHandler(EmailActionEvent, value); }
    }

    public EmailMessageViewModel? SelectedEmail
    {
        get { return (EmailMessageViewModel?)GetValue(SelectedEmailProperty); }
        set { SetValue(SelectedEmailProperty, value); }
    }

    public ModernEmailDetail()
    {
        InitializeComponent();
        DataContext = this;
    }

    private static void OnSelectedEmailChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernEmailDetail control)
        {
            control.UpdateEmailBody();
        }
    }

    private void UpdateEmailBody()
    {
        if (SelectedEmail == null)
        {
            EmailBodyBrowser.NavigateToString("<html><body></body></html>");
            return;
        }

        var message = SelectedEmail;
        var htmlContent = !string.IsNullOrEmpty(message.HtmlBody)
            ? message.HtmlBody
            : $"<html><body style='font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif; line-height: 1.6; color: #374151; padding: 20px;'><pre style='white-space: pre-wrap; font-family: inherit;'>{System.Net.WebUtility.HtmlEncode(message.TextBody)}</pre></body></html>";

        // Add modern styling to HTML content
        if (!htmlContent.Contains("<style>") && !htmlContent.Contains("font-family"))
        {
            htmlContent = htmlContent.Replace("<body", 
                "<body style='font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif; line-height: 1.6; color: #374151; padding: 20px; margin: 0;'");
        }

        EmailBodyBrowser.NavigateToString(htmlContent);
    }

    private void StarButton_Click(object sender, RoutedEventArgs e)
    {
        if (SelectedEmail != null)
        {
            var args = new EmailActionEventArgs(EmailActionEvent, this)
            {
                Action = "Star",
                Email = SelectedEmail
            };
            RaiseEvent(args);
        }
    }

    public void RefreshContent()
    {
        UpdateEmailBody();
        OnPropertyChanged(nameof(SelectedEmail));
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class EmailActionEventArgs : RoutedEventArgs
{
    public string Action { get; set; } = string.Empty;
    public EmailMessageViewModel? Email { get; set; }

    public EmailActionEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
    {
    }
}
