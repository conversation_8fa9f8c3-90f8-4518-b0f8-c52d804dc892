import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { X, Plus, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { apiRequest } from "@/lib/api";
import PriorityBadge from "@/components/ui/priority-badge";
import { cn } from "@/lib/utils";
import type { Task } from "@shared/schema";

interface TaskPanelProps {
  onClose: () => void;
}

export default function TaskPanel({ onClose }: TaskPanelProps) {
  const [newTaskTitle, setNewTaskTitle] = useState("");
  const [showAddTask, setShowAddTask] = useState(false);
  const queryClient = useQueryClient();

  const { data: tasks } = useQuery<Task[]>({
    queryKey: ["/api/tasks"],
    queryFn: () => apiRequest("GET", "/api/tasks").then(res => res.json()),
  });

  const completeMutation = useMutation({
    mutationFn: (taskId: number) => apiRequest("POST", `/api/tasks/${taskId}/complete`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/tasks"] });
    },
  });

  const createMutation = useMutation({
    mutationFn: (title: string) => apiRequest("POST", "/api/tasks", { title }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/tasks"] });
      setNewTaskTitle("");
      setShowAddTask(false);
    },
  });

  const handleCompleteTask = (taskId: number) => {
    completeMutation.mutate(taskId);
  };

  const handleCreateTask = () => {
    if (newTaskTitle.trim()) {
      createMutation.mutate(newTaskTitle.trim());
    }
  };

  const todaysTasks = tasks?.filter(task => {
    if (task.completed) return false;
    if (!task.dueDate) return true; // Tasks without due date are shown as today's tasks
    const today = new Date();
    const taskDate = new Date(task.dueDate);
    return taskDate.toDateString() === today.toDateString();
  }) || [];

  const completedTasks = tasks?.filter(task => task.completed) || [];

  return (
    <div className="fixed bottom-6 right-6 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 glass-effect">
      <Card className="border-0 shadow-none">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
              Today's Tasks
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="max-h-80 overflow-y-auto">
          <div className="space-y-3">
            {/* Add new task */}
            {showAddTask ? (
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Enter task title..."
                  value={newTaskTitle}
                  onChange={(e) => setNewTaskTitle(e.target.value)}
                  onKeyPress={(e) => e.key === "Enter" && handleCreateTask()}
                  className="flex-1"
                />
                <Button size="sm" onClick={handleCreateTask}>
                  <Check className="w-4 h-4" />
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowAddTask(false)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => setShowAddTask(true)}
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Task
              </Button>
            )}

            {/* Today's tasks */}
            {todaysTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover-lift"
              >
                <Checkbox
                  checked={task.completed}
                  onCheckedChange={() => handleCompleteTask(task.id)}
                />
                <div className="flex-1">
                  <p className={cn(
                    "text-sm font-medium",
                    task.completed 
                      ? "text-gray-500 dark:text-gray-400 line-through" 
                      : "text-gray-900 dark:text-white"
                  )}>
                    {task.title}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <PriorityBadge priority={task.priority as "low" | "medium" | "high"} />
                    {task.dueDate && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Due {new Date(task.dueDate).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* Completed tasks */}
            {completedTasks.slice(0, 3).map((task) => (
              <div
                key={task.id}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg opacity-60"
              >
                <Checkbox checked={true} disabled />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400 line-through">
                    {task.title}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 rounded-full">
                      Completed
                    </span>
                    {task.completedAt && (
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(task.completedAt).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {todaysTasks.length === 0 && completedTasks.length === 0 && (
              <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                <Check className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No tasks for today</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
