import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, ChevronLeft, ChevronRight, Calendar as CalendarIcon, Users, Clock, List, Grid3X3, MapPin, X } from "lucide-react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths, isToday, startOfWeek, endOfWeek, addDays, isAfter, isBefore } from "date-fns";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { apiRequest } from "@/lib/api";
import UnifiedAiPanel from "@/components/ai/unified-ai-panel";
import type { CalendarEvent } from "@shared/schema";

export default function Calendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<"month" | "week" | "agenda">("month");
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

  const { data: events, isLoading } = useQuery<CalendarEvent[]>({
    queryKey: ["/api/calendar"],
    queryFn: () => apiRequest("GET", "/api/calendar").then(res => res.json()),
  });

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === "prev") {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const getEventsForDate = (date: number) => {
    if (!events) return [];
    
    const targetDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), date);
    return events.filter(event => {
      const eventDate = new Date(event.startTime);
      return eventDate.toDateString() === targetDate.toDateString();
    });
  };

  const getUpcomingEvents = () => {
    if (!events) return [];
    const now = new Date();
    return events
      .filter(event => new Date(event.startTime) >= now)
      .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
      .slice(0, 10);
  };

  const getWeekEvents = () => {
    if (!events) return [];
    const weekStart = startOfWeek(currentDate);
    const weekEnd = endOfWeek(currentDate);
    
    return events.filter(event => {
      const eventDate = new Date(event.startTime);
      return eventDate >= weekStart && eventDate <= weekEnd;
    });
  };

  const formatEventTime = (event: CalendarEvent) => {
    const start = new Date(event.startTime);
    const end = new Date(event.endTime);
    
    if (start.toDateString() === end.toDateString()) {
      return `${format(start, "h:mm a")} - ${format(end, "h:mm a")}`;
    } else {
      return `${format(start, "MMM d, h:mm a")} - ${format(end, "MMM d, h:mm a")}`;
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "meeting": return "bg-blue-500";
      case "call": return "bg-green-500";
      case "event": return "bg-purple-500";
      case "reminder": return "bg-orange-500";
      default: return "bg-gray-500";
    }
  };

  const daysInMonth = getDaysInMonth(currentDate);
  const firstDay = getFirstDayOfMonth(currentDate);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const emptyDays = Array.from({ length: firstDay }, (_, i) => i);

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Calendar</h2>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => navigateMonth("prev")}>
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <span className="text-lg font-medium text-gray-900 dark:text-white min-w-48 text-center">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </span>
              <Button variant="outline" size="sm" onClick={() => navigateMonth("next")}>
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* View Mode Selector */}
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <Button 
                variant={viewMode === "month" ? "default" : "ghost"} 
                size="sm"
                onClick={() => setViewMode("month")}
              >
                <Grid3X3 className="w-4 h-4 mr-1" />
                Month
              </Button>
              <Button 
                variant={viewMode === "week" ? "default" : "ghost"} 
                size="sm"
                onClick={() => setViewMode("week")}
              >
                <CalendarIcon className="w-4 h-4 mr-1" />
                Week
              </Button>
              <Button 
                variant={viewMode === "agenda" ? "default" : "ghost"} 
                size="sm"
                onClick={() => setViewMode("agenda")}
              >
                <List className="w-4 h-4 mr-1" />
                Agenda
              </Button>
            </div>
            
            <Button variant="outline" size="sm" onClick={() => setCurrentDate(new Date())}>
              Today
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Event
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content - Split View */}
      <div className="flex-1 flex overflow-hidden">
        {/* Calendar Panel */}
        <div className={`${selectedEvent ? 'w-2/3' : 'w-full'} overflow-y-auto transition-all duration-300`}>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="p-6">
              <>
            {/* Month View */}
            {viewMode === "month" && (
              <Card>
                <CardContent className="p-6">
                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-1 mb-4">
                    {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(day => (
                      <div key={day} className="p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
                        {day}
                      </div>
                    ))}
                  </div>
                  
                  <div className="grid grid-cols-7 gap-1">
                    {emptyDays.map(day => (
                      <div key={`empty-${day}`} className="aspect-square p-2"></div>
                    ))}
                    
                    {days.map(day => {
                      const dayEvents = getEventsForDate(day);
                      const isCurrentDay = isToday(new Date(currentDate.getFullYear(), currentDate.getMonth(), day));
                      
                      return (
                        <div key={day} className={cn(
                          "aspect-square p-2 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",
                          isCurrentDay && "bg-blue-50 dark:bg-blue-900/20 border-blue-300"
                        )}>
                          <div className={cn(
                            "text-sm font-medium",
                            isCurrentDay ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"
                          )}>
                            {day}
                          </div>
                          <div className="mt-1 space-y-1">
                            {dayEvents.slice(0, 3).map((event, index) => (
                              <div 
                                key={index} 
                                className={cn(
                                  "text-xs text-white px-2 py-1 rounded truncate cursor-pointer hover:opacity-80",
                                  getEventTypeColor(event.type),
                                  selectedEvent?.id === event.id && "ring-2 ring-white ring-opacity-50"
                                )}
                                onClick={() => setSelectedEvent(event)}
                              >
                                {event.title}
                              </div>
                            ))}
                            {dayEvents.length > 3 && (
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                +{dayEvents.length - 3} more
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Week View */}
            {viewMode === "week" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CalendarIcon className="w-5 h-5" />
                    <span>Week of {format(startOfWeek(currentDate), "MMM d, yyyy")}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-7 gap-4">
                    {eachDayOfInterval({
                      start: startOfWeek(currentDate),
                      end: endOfWeek(currentDate)
                    }).map(day => {
                      const dayEvents = events?.filter(event => 
                        isSameDay(new Date(event.startTime), day)
                      ) || [];
                      
                      return (
                        <div key={day.toString()} className="space-y-2">
                          <div className={cn(
                            "text-center p-2 rounded-lg",
                            isToday(day) && "bg-blue-100 dark:bg-blue-900/30"
                          )}>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {format(day, "EEE")}
                            </div>
                            <div className={cn(
                              "text-lg font-semibold",
                              isToday(day) ? "text-blue-600 dark:text-blue-400" : "text-gray-900 dark:text-white"
                            )}>
                              {format(day, "d")}
                            </div>
                          </div>
                          
                          <div className="space-y-1">
                            {dayEvents.map(event => (
                              <div 
                                key={event.id}
                                className={cn(
                                  "text-xs p-2 rounded cursor-pointer hover:opacity-80 text-white",
                                  getEventTypeColor(event.type)
                                )}
                                onClick={() => setSelectedEvent(event)}
                              >
                                <div className="font-medium truncate">{event.title}</div>
                                <div className="opacity-90">
                                  {format(new Date(event.startTime), "h:mm a")}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Agenda View */}
            {viewMode === "agenda" && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <List className="w-5 h-5" />
                      <span>Upcoming Events</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {getUpcomingEvents().length === 0 ? (
                      <div className="text-center py-8">
                        <CalendarIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500 dark:text-gray-400">No upcoming events</p>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {getUpcomingEvents().map(event => {
                          const eventDate = new Date(event.startTime);
                          const isEventToday = isToday(eventDate);
                          
                          return (
                            <div 
                              key={event.id}
                              className={cn(
                                "flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-all",
                                selectedEvent?.id === event.id && "ring-2 ring-blue-500 ring-opacity-50"
                              )}
                              onClick={() => setSelectedEvent(event)}
                            >
                              <div className={cn(
                                "w-3 h-3 rounded-full",
                                getEventTypeColor(event.type)
                              )} />
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h4 className="font-medium text-gray-900 dark:text-white truncate">
                                    {event.title}
                                  </h4>
                                  <Badge variant="outline" className="ml-2">
                                    {event.type}
                                  </Badge>
                                </div>
                                
                                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500 dark:text-gray-400">
                                  <span className="flex items-center space-x-1">
                                    <Clock className="w-3 h-3" />
                                    <span>
                                      {isEventToday ? "Today" : format(eventDate, "MMM d")} • {formatEventTime(event)}
                                    </span>
                                  </span>
                                  
                                  {event.location && (
                                    <span className="flex items-center space-x-1">
                                      <MapPin className="w-3 h-3" />
                                      <span className="truncate">{event.location}</span>
                                    </span>
                                  )}
                                  
                                  {event.attendees && Array.isArray(event.attendees) && event.attendees.length > 0 && (
                                    <span className="flex items-center space-x-1">
                                      <Users className="w-3 h-3" />
                                      <span>{event.attendees.length} attendees</span>
                                    </span>
                                  )}
                                </div>
                                
                                {event.description && (
                                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                                    {event.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
              </>
            </div>
          )}
        </div>

        {/* Event Detail and Context Panel */}
        {selectedEvent && (
          <div className="flex-1 flex border-l border-gray-200 dark:border-gray-700">
            {/* Event Detail */}
            <div className="flex-1 flex flex-col">
              <div className="border-b bg-white dark:bg-gray-800 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {selectedEvent.title}
                  </h2>
                  <Button variant="ghost" size="sm" onClick={() => setSelectedEvent(null)}>
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <div className="flex-1 overflow-y-auto p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                      Event Details
                    </h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {formatEventTime(selectedEvent)}
                        </span>
                      </div>
                      
                      {selectedEvent.location && (
                        <div className="flex items-center space-x-3">
                          <MapPin className="w-4 h-4 text-gray-500" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {selectedEvent.location}
                          </span>
                        </div>
                      )}
                      
                      <div className="flex items-center space-x-3">
                        <CalendarIcon className="w-4 h-4 text-gray-500" />
                        <Badge variant="outline">{selectedEvent.type}</Badge>
                      </div>
                    </div>
                  </div>
                  
                  {selectedEvent.description && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Description
                      </h3>
                      <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {selectedEvent.description}
                      </p>
                    </div>
                  )}
                  
                  {selectedEvent.attendees && Array.isArray(selectedEvent.attendees) && selectedEvent.attendees.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Attendees ({selectedEvent.attendees.length})
                      </h3>
                      <div className="space-y-2">
                        {selectedEvent.attendees.map((attendee, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Users className="w-4 h-4 text-gray-500" />
                            <span className="text-sm text-gray-700 dark:text-gray-300">
                              {attendee}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* AI Insights Panel */}
            <div className="w-80 bg-gray-50 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
              <UnifiedAiPanel context="calendar" itemId={selectedEvent.id} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
