import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RefreshCw, Plus, Search, Mail, Globe, ChevronDown } from "lucide-react";
import EmailList from "@/components/email/email-list";
import EmailDetailView from "@/components/email/email-detail-view";
import ComposeModal from "@/components/email/compose-modal";
import UnifiedAiPanel from "@/components/ai/unified-ai-panel";
import AccountSwitcher from "@/components/email/account-switcher";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { apiRequest } from "@/lib/api";
import type { Email } from "@shared/schema";

export default function Inbox() {
  const [filter, setFilter] = useState<"all" | "unread" | "important">("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const [selectedAccountId, setSelectedAccountId] = useState<number | undefined>();

  // Get available inboxes
  const { data: inboxes = [] } = useQuery<any[]>({
    queryKey: ["/api/inboxes"],
  });

  // Auto-select default account if none selected
  const defaultInbox = inboxes.find(inbox => inbox.isDefault) || inboxes[0];
  const currentAccountId = selectedAccountId || defaultInbox?.id;

  const { data: emails, isLoading, refetch } = useQuery<Email[]>({
    queryKey: ["/api/emails", selectedAccountId],
    queryFn: () => {
      const url = selectedAccountId ? `/api/emails?inboxId=${selectedAccountId}` : "/api/emails";
      return apiRequest("GET", url).then(res => res.json());
    },
  });

  const filteredEmails = emails?.filter(email => {
    const matchesSearch = !searchQuery || 
      email.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      email.sender.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = 
      filter === "all" || 
      (filter === "unread" && !email.isRead) ||
      (filter === "important" && email.isImportant);
    
    return matchesSearch && matchesFilter;
  }) || [];

  const handleRefresh = () => {
    refetch();
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex flex-col">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
                {selectedAccountId ? 
                  inboxes.find(inbox => inbox.id === selectedAccountId)?.name || "Inbox" : 
                  "Unified Inbox"
                }
              </h2>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {selectedAccountId ? 
                  inboxes.find(inbox => inbox.id === selectedAccountId)?.email :
                  `All accounts • ${inboxes.length} connected`
                }
              </span>
            </div>
            <div className="flex items-center space-x-4">
              {/* Account Switcher */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center space-x-2">
                    {selectedAccountId ? (
                      <>
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>{inboxes.find(inbox => inbox.id === selectedAccountId)?.provider || "Account"}</span>
                      </>
                    ) : (
                      <>
                        <Globe className="w-4 h-4" />
                        <span>All Accounts</span>
                      </>
                    )}
                    <ChevronDown className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-64">
                  <DropdownMenuItem 
                    onClick={() => setSelectedAccountId(undefined)}
                    className={selectedAccountId === undefined ? "bg-primary/10" : ""}
                  >
                    <Globe className="w-4 h-4 mr-2" />
                    <div className="flex flex-col">
                      <span className="font-medium">Unified Inbox</span>
                      <span className="text-xs text-gray-500">All accounts • {emails?.length || 0} emails</span>
                    </div>
                  </DropdownMenuItem>
                  {inboxes.map((inbox: any) => (
                    <DropdownMenuItem 
                      key={inbox.id}
                      onClick={() => setSelectedAccountId(inbox.id)}
                      className={selectedAccountId === inbox.id ? "bg-primary/10" : ""}
                    >
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                      <div className="flex flex-col">
                        <span className="font-medium">{inbox.name}</span>
                        <span className="text-xs text-gray-500">{inbox.email} • {inbox.unreadCount || 0} unread</span>
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="flex items-center space-x-2">
              <span className="px-2 py-1 text-xs bg-primary text-white rounded-full">
                {filteredEmails.filter(e => !e.isRead).length} New
              </span>
              <Button
                variant={filter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("all")}
              >
                All
              </Button>
              <Button
                variant={filter === "unread" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("unread")}
              >
                Unread
              </Button>
              <Button
                variant={filter === "important" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("important")}
              >
                Important
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search emails..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4" />
            </Button>
            <Button size="sm" onClick={() => setIsComposeOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Compose
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content - Split View */}
      <div className="flex-1 flex overflow-hidden">
        {/* Email List Panel */}
        <div className={`${selectedEmail ? 'w-1/3' : 'w-full'} border-r border-gray-200 dark:border-gray-700 overflow-y-auto transition-all duration-300`}>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredEmails.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery ? "No emails found matching your search" : "No emails found"}
                </p>
              </div>
            </div>
          ) : (
            <EmailList emails={filteredEmails} onEmailClick={setSelectedEmail} selectedEmailId={selectedEmail?.id} />
          )}
        </div>

        {/* Email Detail and Context Panel */}
        {selectedEmail && (
          <div className="flex-1 flex">
            {/* Email Detail */}
            <div className="flex-1 flex flex-col">
              <EmailDetailView
                email={selectedEmail}
                onClose={() => setSelectedEmail(null)}
                relatedEmails={emails?.filter(e => 
                  e.id !== selectedEmail.id && 
                  (e.subject.includes(selectedEmail.subject.replace('Re: ', '')) ||
                   selectedEmail.subject.includes(e.subject.replace('Re: ', '')))
                ) || []}
                isInlineView={true}
              />
            </div>
            
            {/* AI Insights Panel */}
            <div className="w-80 bg-gray-50 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
              <UnifiedAiPanel context="email" itemId={selectedEmail.id} />
            </div>
          </div>
        )}
      </div>

      {/* Compose Modal */}
      <ComposeModal 
        isOpen={isComposeOpen}
        onClose={() => setIsComposeOpen(false)}
      />
    </div>
  );
}
