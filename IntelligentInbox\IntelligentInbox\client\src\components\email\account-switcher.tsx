import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Check, ChevronsUpDown, Plus, Mail, AlertCircle, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface Inbox {
  id: number;
  name: string;
  email: string;
  provider: string;
  isDefault: boolean;
  isActive: boolean;
  syncStatus: string;
  unreadCount: number;
  totalCount: number;
  errorMessage?: string;
}

interface AccountSwitcherProps {
  selectedAccountId?: number;
  onAccountChange: (accountId: number) => void;
}

const providerIcons: Record<string, string> = {
  gmail: "🌟",
  outlook: "📧",
  yahoo: "💌",
  imap: "📬",
  exchange: "🏢"
};

const statusColors: Record<string, string> = {
  idle: "bg-green-500",
  syncing: "bg-blue-500",
  error: "bg-red-500"
};

export function AccountSwitcher({ selectedAccountId, onAccountChange }: AccountSwitcherProps) {
  const [open, setOpen] = useState(false);

  const { data: inboxes = [], isLoading } = useQuery<Inbox[]>({
    queryKey: ["/api/inboxes"],
  });

  const selectedInbox = inboxes.find(inbox => inbox.id === selectedAccountId) || inboxes.find(inbox => inbox.isDefault) || inboxes[0];

  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case "syncing":
        return <Loader2 className="w-3 h-3 animate-spin" />;
      case "error":
        return <AlertCircle className="w-3 h-3 text-red-500" />;
      default:
        return <div className={cn("w-2 h-2 rounded-full", statusColors[status] || "bg-gray-500")} />;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 p-2">
        <Loader2 className="w-4 h-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading accounts...</span>
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedInbox ? (
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <span className="text-lg">{providerIcons[selectedInbox.provider] || "📧"}</span>
              <div className="flex flex-col text-left min-w-0">
                <span className="text-sm font-medium truncate">{selectedInbox.name}</span>
                <span className="text-xs text-muted-foreground truncate">{selectedInbox.email}</span>
              </div>
              <div className="flex items-center space-x-1 ml-auto">
                {getSyncStatusIcon(selectedInbox.syncStatus)}
                {selectedInbox.unreadCount > 0 && (
                  <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                    {selectedInbox.unreadCount}
                  </Badge>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span>Select Account</span>
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        <Command>
          <CommandInput placeholder="Search accounts..." />
          <CommandList>
            <CommandEmpty>No accounts found.</CommandEmpty>
            <CommandGroup heading="Email Accounts">
              {inboxes.map((inbox) => (
                <CommandItem
                  key={inbox.id}
                  value={`${inbox.name} ${inbox.email}`}
                  onSelect={() => {
                    onAccountChange(inbox.id);
                    setOpen(false);
                  }}
                >
                  <div className="flex items-center space-x-3 w-full">
                    <span className="text-lg">{providerIcons[inbox.provider] || "📧"}</span>
                    <div className="flex flex-col flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium truncate">{inbox.name}</span>
                        {inbox.isDefault && (
                          <Badge variant="outline" className="text-xs">Default</Badge>
                        )}
                      </div>
                      <span className="text-xs text-muted-foreground truncate">{inbox.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getSyncStatusIcon(inbox.syncStatus)}
                      {inbox.unreadCount > 0 && (
                        <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                          {inbox.unreadCount}
                        </Badge>
                      )}
                      <Check
                        className={cn(
                          "h-4 w-4",
                          selectedInbox?.id === inbox.id ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem>
                <Plus className="mr-2 h-4 w-4" />
                <span>Add Account</span>
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}