# Ollama Integration Guide

This guide explains how to integrate Ollama for local AI processing on Windows with GPU support.

## Prerequisites

### Windows Setup
- Windows 10/11 with dedicated GPU (NVIDIA recommended)
- GPU drivers installed and updated
- PowerShell or Command Prompt access

### Ollama Installation
1. Download Ollama for Windows from https://ollama.ai/download
2. Install and verify with: `ollama --version`
3. Pull your preferred model (e.g., `ollama pull llama2` or `ollama pull mistral`)

## Integration Steps

### 1. Update AI Service

Replace the mock implementation in `server/services/ai.ts` with Ollama API calls:

```typescript
// Add to the top of server/services/ai.ts
const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

// Replace summarizeEmail function
export async function summarizeEmail(subject: string, body: string): Promise<EmailSummary> {
  try {
    const response = await fetch(`${OLLAMA_BASE_URL}/api/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'llama2', // or your preferred model
        prompt: `Analyze this email and provide a JSON summary:
        
Subject: ${subject}
Body: ${body}

Please respond with JSON in this format:
{
  "summary": "Brief summary of the email",
  "keyPoints": ["key point 1", "key point 2"],
  "actionItems": ["action item 1", "action item 2"],
  "sentiment": "positive|negative|neutral",
  "priority": "low|medium|high"
}`,
        stream: false
      })
    });

    const result = await response.json();
    const analysis = JSON.parse(result.response);
    
    return {
      summary: analysis.summary || "No summary available",
      keyPoints: analysis.keyPoints || [],
      actionItems: analysis.actionItems || [],
      sentiment: analysis.sentiment || "neutral",
      priority: analysis.priority || "medium"
    };
  } catch (error) {
    console.error("Error with Ollama:", error);
    // Fallback to current mock implementation
    return getCurrentMockResponse(subject, body);
  }
}
```

### 2. Environment Configuration

Add to your environment variables:
```
OLLAMA_BASE_URL=http://localhost:11434
```

### 3. Model Selection

Choose a model based on your needs:
- **Fast processing**: `tinyllama` or `phi`
- **Balanced**: `llama2` or `mistral`
- **High quality**: `llama2:13b` or `codellama`

### 4. GPU Optimization

For NVIDIA GPUs, Ollama automatically uses CUDA if available. Monitor GPU usage with:
```
nvidia-smi
```

### 5. Testing

1. Start Ollama: `ollama serve`
2. Test the model: `ollama run llama2 "Hello, world!"`
3. Restart your application to use the new AI service

## Performance Considerations

- **Model Size**: Larger models provide better results but require more GPU memory
- **Context Length**: Longer emails may require models with larger context windows
- **Batch Processing**: Consider implementing queue system for multiple simultaneous requests
- **Caching**: Cache AI responses for frequently accessed content

## Troubleshooting

### Common Issues
- **Port conflicts**: Ensure port 11434 is available
- **GPU not detected**: Verify CUDA installation
- **Model not found**: Ensure model is pulled with `ollama pull [model-name]`
- **Memory issues**: Use smaller models or increase system RAM

### Monitoring
- Check Ollama logs: `ollama logs`
- Monitor GPU usage: `nvidia-smi -l 1`
- Application logs will show AI service errors

## Security Notes

- Ollama runs locally, so no data leaves your system
- API endpoint is localhost-only by default
- Consider firewall rules if exposing to network
- Regular model updates for security patches

## Next Steps

After integration:
1. Test all AI features (email summarization, relationship detection, suggestions)
2. Fine-tune prompts for better results
3. Consider implementing caching for performance
4. Monitor system resources and adjust model size as needed