using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EmailClient.Models;
using EmailClient.Services;
using EmailClient.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Windows;

namespace EmailClient.ViewModels;

public partial class MainWindowViewModel : ViewModelBase
{
    private readonly IEmailService _emailService;
    private readonly IAccountService _accountService;
    private readonly IMultiAccountService _multiAccountService;
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly ISyncService _syncService;

    [ObservableProperty]
    private ObservableCollection<FolderTreeItemViewModel> _folderTree = new();

    [ObservableProperty]
    private ObservableCollection<EmailMessageViewModel> _emailMessages = new();

    [ObservableProperty]
    private EmailMessageViewModel? _selectedMessage;

    [ObservableProperty]
    private object? _selectedFolder;

    [ObservableProperty]
    private string _searchQuery = string.Empty;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private int _totalEmailCount;

    public MainWindowViewModel(IEmailService emailService, IAccountService accountService, IMultiAccountService multiAccountService, ILogger<MainWindowViewModel> logger, IServiceProvider serviceProvider, ISyncService syncService)
    {
        _emailService = emailService;
        _accountService = accountService;
        _multiAccountService = multiAccountService;
        _logger = logger;
        _serviceProvider = serviceProvider;
        _syncService = syncService;

        // Subscribe to sync events
        _syncService.SyncCompleted += OnSyncCompleted;

        // Initialize synchronously to avoid async issues
        InitializeSync();
    }

    private void OnSyncCompleted(object? sender, SyncCompletedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            if (e.Success)
            {
                StatusMessage = $"Sync completed - {e.AccountsSynced} accounts synced";
                _ = LoadMessagesForSelectedFolderAsync(); // Refresh current view
            }
            else
            {
                StatusMessage = $"Sync failed: {e.ErrorMessage}";
            }
        });
    }

    private void InitializeSync()
    {
        try
        {
            StatusMessage = "Ready";

            // Add a simple unified inbox entry
            FolderTree.Add(new FolderTreeItemViewModel
            {
                Name = "Unified Inbox",
                FullName = "unified",
                Type = FolderType.Inbox,
                IsAccount = false
            });

            TotalEmailCount = 0;

            // Load data asynchronously in background
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(1000); // Give UI time to load
                    await LoadDataAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to load data in background");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize main window");
            StatusMessage = "Error during initialization";
        }
    }

    private async Task LoadDataAsync()
    {
        try
        {
            // Create a new scope for this background operation
            using var scope = _serviceProvider.CreateScope();
            var accountService = scope.ServiceProvider.GetRequiredService<IAccountService>();
            var multiAccountService = scope.ServiceProvider.GetRequiredService<IMultiAccountService>();

            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Loading accounts...");
            await LoadFolderTreeAsync(accountService);

            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Loading emails...");
            await LoadUnifiedInboxAsync(multiAccountService);

            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Ready");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load data");
            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Error loading data");
        }
    }

    private async Task LoadFolderTreeAsync(IAccountService? accountService = null)
    {
        try
        {
            // Use provided service or create a new scope
            if (accountService == null)
            {
                using var scope = _serviceProvider.CreateScope();
                accountService = scope.ServiceProvider.GetRequiredService<IAccountService>();
                await LoadFolderTreeInternalAsync(accountService);
            }
            else
            {
                await LoadFolderTreeInternalAsync(accountService);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load folder tree");
            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Failed to load accounts");
        }
    }

    private async Task LoadFolderTreeInternalAsync(IAccountService accountService)
    {
        var accounts = await accountService.GetAllAccountsAsync();

        // Add accounts and their folders (unified inbox already added in InitializeSync)
        foreach (var account in accounts)
        {
            var accountNode = FolderTreeItemViewModel.FromAccount(account);
            var folders = await accountService.GetAccountFoldersAsync(account.Id);

            foreach (var folder in folders)
            {
                var folderNode = FolderTreeItemViewModel.FromFolder(folder);
                accountNode.Children.Add(folderNode);
            }

            // Update UI on main thread
            Application.Current.Dispatcher.Invoke(() => FolderTree.Add(accountNode));
        }
    }

    private async Task LoadUnifiedInboxAsync(IMultiAccountService? multiAccountService = null)
    {
        try
        {
            UnifiedInboxResult result;

            // Use provided service or create a new scope
            if (multiAccountService == null)
            {
                using var scope = _serviceProvider.CreateScope();
                multiAccountService = scope.ServiceProvider.GetRequiredService<IMultiAccountService>();
                result = await multiAccountService.GetUnifiedInboxAsync(50, 1);
            }
            else
            {
                result = await multiAccountService.GetUnifiedInboxAsync(50, 1);
            }

            // Update UI on main thread
            Application.Current.Dispatcher.Invoke(() =>
            {
                EmailMessages.Clear();

                foreach (var message in result.Messages)
                {
                    EmailMessages.Add(new EmailMessageViewModel(message));
                }

                TotalEmailCount = result.TotalCount;

                // Update status with account breakdown
                if (result.MessageCountByAccount.Any())
                {
                    var accountBreakdown = string.Join(", ",
                        result.MessageCountByAccount.Select(kvp => $"Account {kvp.Key}: {kvp.Value}"));
                    StatusMessage = $"Unified Inbox loaded - {result.TotalCount} total ({result.UnreadCount} unread)";
                }
                else
                {
                    StatusMessage = "Ready - No emails found";
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load unified inbox");
            Application.Current.Dispatcher.Invoke(() => StatusMessage = "Failed to load unified inbox");
        }
    }

    partial void OnSelectedFolderChanged(object? value)
    {
        _ = LoadMessagesForSelectedFolderAsync();
    }

    partial void OnSelectedMessageChanged(EmailMessageViewModel? value)
    {
        OnPropertyChanged(nameof(MarkReadButtonText));
        _ = LoadMessageBodyAsync();
    }

    private async Task LoadMessageBodyAsync()
    {
        if (SelectedMessage == null) return;

        try
        {
            // Mark as read when viewing
            if (!SelectedMessage.IsRead)
            {
                await _emailService.MarkAsReadAsync(SelectedMessage.Id);
                SelectedMessage.GetModel().IsRead = true;
                OnPropertyChanged(nameof(MarkReadButtonText));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to mark message as read");
        }
    }

    private async Task LoadMessagesForSelectedFolderAsync()
    {
        if (SelectedFolder is not FolderTreeItemViewModel folder)
            return;

        try
        {
            StatusMessage = "Loading messages...";
            EmailMessages.Clear();

            IEnumerable<EmailMessage> messages;

            if (folder.FullName == "unified")
            {
                messages = await _emailService.GetUnifiedInboxAsync();
            }
            else if (folder.IsAccount && folder.AccountId.HasValue)
            {
                messages = await _emailService.GetMessagesForAccountAsync(folder.AccountId.Value);
            }
            else if (folder.FolderId.HasValue)
            {
                messages = await _emailService.GetMessagesForFolderAsync(folder.FolderId.Value);
            }
            else
            {
                return;
            }

            foreach (var message in messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = EmailMessages.Count;
            StatusMessage = "Ready";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load messages for folder {FolderName}", folder.Name);
            StatusMessage = "Error loading messages";
        }
    }

    [RelayCommand]
    private async Task AddAccount()
    {
        try
        {
            var accountSetupViewModel = _serviceProvider.GetRequiredService<AccountSetupViewModel>();
            var accountSetupWindow = new AccountSetupWindow(accountSetupViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            accountSetupViewModel.SetWindow(accountSetupWindow);
            accountSetupWindow.ShowDialog();

            if (accountSetupViewModel.DialogResult)
            {
                StatusMessage = "Account added successfully";
                await LoadFolderTreeAsync();
                await LoadUnifiedInboxAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open account setup dialog");
            StatusMessage = "Failed to open account setup";
        }
    }

    [RelayCommand]
    private async Task SyncAll()
    {
        try
        {
            StatusMessage = "Syncing all accounts...";
            await _syncService.SyncNowAsync();
            await LoadFolderTreeAsync();
            // Note: LoadMessagesForSelectedFolderAsync will be called by OnSyncCompleted
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync all accounts");
            StatusMessage = "Sync failed";
        }
    }

    [RelayCommand]
    private async Task Refresh()
    {
        await LoadMessagesForSelectedFolderAsync();
    }

    [RelayCommand]
    private async Task Search()
    {
        if (string.IsNullOrWhiteSpace(SearchQuery))
        {
            await LoadMessagesForSelectedFolderAsync();
            return;
        }

        try
        {
            StatusMessage = "Searching across all accounts...";
            var searchResult = await _multiAccountService.SearchAcrossAccountsAsync(SearchQuery, 100);
            EmailMessages.Clear();

            foreach (var message in searchResult.Messages)
            {
                EmailMessages.Add(new EmailMessageViewModel(message));
            }

            TotalEmailCount = searchResult.TotalResults;

            // Show search results breakdown by account
            var accountBreakdown = string.Join(", ",
                searchResult.ResultsByAccount.Select(kvp => $"Account {kvp.Key}: {kvp.Value}"));
            StatusMessage = $"Found {searchResult.TotalResults} results in {searchResult.SearchDuration.TotalMilliseconds:F0}ms ({accountBreakdown})";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Search failed for query: {Query}", SearchQuery);
            StatusMessage = "Search failed";
        }
    }

    [RelayCommand]
    private void ShowDashboard()
    {
        try
        {
            var dashboardViewModel = _serviceProvider.GetRequiredService<AccountDashboardViewModel>();
            var dashboardWindow = new Views.AccountDashboardWindow(dashboardViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            dashboardWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open account dashboard");
            StatusMessage = "Failed to open account dashboard";
        }
    }

    [RelayCommand]
    private void Settings()
    {
        try
        {
            var settingsViewModel = _serviceProvider.GetRequiredService<SettingsViewModel>();
            var settingsWindow = new Views.SettingsWindow(settingsViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            settingsViewModel.SetWindow(settingsWindow);
            settingsWindow.ShowDialog();

            if (settingsViewModel.DialogResult)
            {
                StatusMessage = "Settings saved successfully";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open settings dialog");
            StatusMessage = "Failed to open settings";
        }
    }

    [RelayCommand]
    private void Exit()
    {
        Application.Current.Shutdown();
    }

    [RelayCommand]
    private void ShowContacts()
    {
        try
        {
            var contactsViewModel = _serviceProvider.GetRequiredService<ContactsViewModel>();
            var contactsWindow = new ContactsWindow(contactsViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            contactsWindow.Show();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open contacts window");
            StatusMessage = "Failed to open contacts";
        }
    }

    [RelayCommand]
    private void ShowCalendar()
    {
        try
        {
            var calendarViewModel = _serviceProvider.GetRequiredService<CalendarViewModel>();
            var calendarWindow = new CalendarWindow(calendarViewModel)
            {
                Owner = Application.Current.MainWindow
            };

            calendarWindow.Show();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open calendar window");
            StatusMessage = "Failed to open calendar";
        }
    }

    [RelayCommand]
    private async Task SyncContacts()
    {
        try
        {
            StatusMessage = "Syncing contacts...";
            using var scope = _serviceProvider.CreateScope();
            var contactsService = scope.ServiceProvider.GetRequiredService<IGoogleContactsService>();
            var count = await contactsService.SyncContactsAsync(fullSync: true);
            StatusMessage = $"Synced {count} contacts";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync contacts");
            StatusMessage = "Failed to sync contacts";
        }
    }

    [RelayCommand]
    private async Task SyncCalendar()
    {
        try
        {
            StatusMessage = "Syncing calendar...";
            using var scope = _serviceProvider.CreateScope();
            var calendarService = scope.ServiceProvider.GetRequiredService<IGoogleCalendarService>();
            var count = await calendarService.SyncEventsAsync(fullSync: true);
            StatusMessage = $"Synced {count} calendar events";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync calendar");
            StatusMessage = "Failed to sync calendar";
        }
    }

    [RelayCommand]
    private void ShowLogViewer()
    {
        try
        {
            var logViewer = new Views.LogViewerWindow();
            logViewer.Show();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open log viewer");
            StatusMessage = "Failed to open log viewer";
        }
    }

    [RelayCommand]
    private void About()
    {
        MessageBox.Show("Email Client v1.0\nA unified IMAP email client for Windows", "About", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    public string MarkReadButtonText => SelectedMessage?.IsRead == true ? "Mark Unread" : "Mark Read";

    [RelayCommand]
    private async Task MarkRead()
    {
        if (SelectedMessage == null) return;

        try
        {
            if (SelectedMessage.IsRead)
            {
                await _emailService.MarkAsUnreadAsync(SelectedMessage.Id);
            }
            else
            {
                await _emailService.MarkAsReadAsync(SelectedMessage.Id);
            }

            // Refresh the message list
            await LoadMessagesForSelectedFolderAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to toggle read status");
            StatusMessage = "Failed to update message";
        }
    }

    [RelayCommand]
    private async Task Delete()
    {
        if (SelectedMessage == null) return;

        var result = MessageBox.Show("Are you sure you want to delete this message?", "Confirm Delete", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result != MessageBoxResult.Yes) return;

        try
        {
            await _emailService.DeleteMessageAsync(SelectedMessage.Id);
            await LoadMessagesForSelectedFolderAsync();
            StatusMessage = "Message deleted";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete message");
            StatusMessage = "Failed to delete message";
        }
    }

    [RelayCommand]
    private void Compose()
    {
        try
        {
            var composeWindow = new ComposeWindow();
            composeWindow.Show();
            StatusMessage = "Compose window opened";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open compose window");
            StatusMessage = "Failed to open compose window";
        }
    }

    [RelayCommand]
    private void Reply()
    {
        if (SelectedMessage == null)
        {
            StatusMessage = "No message selected for reply";
            return;
        }

        try
        {
            var composeWindow = new ComposeWindow();
            var viewModel = ((App)Application.Current).ServiceProvider.GetRequiredService<ComposeEmailViewModel>();
            viewModel.SetReplyData(SelectedMessage.GetEmailMessage());
            composeWindow.DataContext = viewModel;
            composeWindow.Show();
            StatusMessage = "Reply window opened";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open reply window");
            StatusMessage = "Failed to open reply window";
        }
    }

    [RelayCommand]
    private void Forward()
    {
        if (SelectedMessage == null)
        {
            StatusMessage = "No message selected for forward";
            return;
        }

        try
        {
            var composeWindow = new ComposeWindow();
            var viewModel = ((App)Application.Current).ServiceProvider.GetRequiredService<ComposeEmailViewModel>();
            viewModel.SetForwardData(SelectedMessage.GetEmailMessage());
            composeWindow.DataContext = viewModel;
            composeWindow.Show();
            StatusMessage = "Forward window opened";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open forward window");
            StatusMessage = "Failed to open forward window";
        }
    }

    public int UnreadEmailCount => EmailMessages.Count(e => !e.IsRead);
}
