using Microsoft.Win32;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;

namespace EmailClient.Services;

public class ThemeService : IThemeService, IDisposable
{
    private AppTheme _currentTheme = AppTheme.System;
    private readonly ISettingsService _settingsService;

    public event PropertyChangedEventHandler? PropertyChanged;
    public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

    public ThemeService(ISettingsService settingsService)
    {
        _settingsService = settingsService;

        // Initialize theme asynchronously
        _ = InitializeAsync();

        // Listen for system theme changes
        SystemEvents.UserPreferenceChanged += OnSystemPreferenceChanged;
    }

    private async Task InitializeAsync()
    {
        try
        {
            // Load saved theme preference
            var savedTheme = await _settingsService.GetStringSettingAsync("Theme", "System");
            if (Enum.TryParse<AppTheme>(savedTheme, out var theme))
            {
                _currentTheme = theme;
            }

            // Apply the theme
            ApplyCurrentTheme();
        }
        catch
        {
            // Use default theme if loading fails
            ApplyCurrentTheme();
        }
    }

    public AppTheme CurrentTheme
    {
        get => _currentTheme;
        private set
        {
            if (_currentTheme != value)
            {
                var previousTheme = _currentTheme;
                _currentTheme = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsDarkMode));
                ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(previousTheme, value));
            }
        }
    }

    public bool IsDarkMode
    {
        get
        {
            return CurrentTheme switch
            {
                AppTheme.Dark => true,
                AppTheme.Light => false,
                AppTheme.System => IsSystemDarkMode(),
                _ => false
            };
        }
    }

    public void SetTheme(AppTheme theme)
    {
        CurrentTheme = theme;
        _ = _settingsService.SetStringSettingAsync("Theme", theme.ToString());
        ApplyCurrentTheme();
    }

    public void ToggleTheme()
    {
        var newTheme = CurrentTheme switch
        {
            AppTheme.Light => AppTheme.Dark,
            AppTheme.Dark => AppTheme.Light,
            AppTheme.System => IsSystemDarkMode() ? AppTheme.Light : AppTheme.Dark,
            _ => AppTheme.Light
        };
        
        SetTheme(newTheme);
    }

    public void ApplySystemTheme()
    {
        SetTheme(AppTheme.System);
    }

    public bool IsSystemDarkMode()
    {
        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            var value = key?.GetValue("AppsUseLightTheme");
            return value is int intValue && intValue == 0;
        }
        catch
        {
            // Default to light mode if we can't read the registry
            return false;
        }
    }

    private void ApplyCurrentTheme()
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            // Remove existing theme resources
            var resourcesToRemove = app.Resources.MergedDictionaries
                .Where(rd => rd.Source?.OriginalString?.Contains("Theme.xaml") == true)
                .ToList();

            foreach (var resource in resourcesToRemove)
            {
                app.Resources.MergedDictionaries.Remove(resource);
            }

            // Add the appropriate theme
            var themeUri = GetThemeResourceUri();
            if (themeUri != null)
            {
                var themeDict = new ResourceDictionary { Source = themeUri };
                app.Resources.MergedDictionaries.Add(themeDict);
            }
        });
    }

    private Uri? GetThemeResourceUri()
    {
        var isDark = CurrentTheme switch
        {
            AppTheme.Dark => true,
            AppTheme.Light => false,
            AppTheme.System => IsSystemDarkMode(),
            _ => false
        };

        var themeName = isDark ? "DarkTheme" : "LightTheme";
        return new Uri($"pack://application:,,,/EmailClient;component/Styles/{themeName}.xaml");
    }

    private void OnSystemPreferenceChanged(object sender, UserPreferenceChangedEventArgs e)
    {
        if (e.Category == UserPreferenceCategory.General && CurrentTheme == AppTheme.System)
        {
            // System theme changed and we're following system theme
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                ApplyCurrentTheme();
                OnPropertyChanged(nameof(IsDarkMode));
            });
        }
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    public void Dispose()
    {
        SystemEvents.UserPreferenceChanged -= OnSystemPreferenceChanged;
    }
}
