using EmailClient.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Win32;
using System.ComponentModel;
using System.Windows;
using System.Windows.Input;

namespace EmailClient.Views;

public partial class ComposeWindow : Window
{
    private ComposeEmailViewModel? _viewModel;
    private bool _isDraft = false;

    public ComposeWindow()
    {
        InitializeComponent();
        InitializeViewModel();
        SetupKeyboardShortcuts();
    }

    public ComposeWindow(string? toAddress = null, string? subject = null, string? body = null) : this()
    {
        if (_viewModel != null)
        {
            if (!string.IsNullOrEmpty(toAddress))
                _viewModel.ToAddresses = toAddress;
            if (!string.IsNullOrEmpty(subject))
                _viewModel.Subject = subject;
            if (!string.IsNullOrEmpty(body))
                _viewModel.Body = body;
        }
    }

    private void InitializeViewModel()
    {
        var serviceProvider = ((App)Application.Current).ServiceProvider;
        _viewModel = serviceProvider.GetRequiredService<ComposeEmailViewModel>();
        DataContext = _viewModel;
        
        _viewModel.PropertyChanged += ViewModel_PropertyChanged;
    }

    private void SetupKeyboardShortcuts()
    {
        // Ctrl+Enter to send
        var sendGesture = new KeyGesture(Key.Enter, ModifierKeys.Control);
        var sendCommand = new RoutedCommand();
        sendCommand.InputGestures.Add(sendGesture);
        CommandBindings.Add(new CommandBinding(sendCommand, (s, e) => SendEmail()));

        // Ctrl+S to save draft
        var saveGesture = new KeyGesture(Key.S, ModifierKeys.Control);
        var saveCommand = new RoutedCommand();
        saveCommand.InputGestures.Add(saveGesture);
        CommandBindings.Add(new CommandBinding(saveCommand, (s, e) => SaveDraft()));

        // Escape to close
        var escapeGesture = new KeyGesture(Key.Escape);
        var escapeCommand = new RoutedCommand();
        escapeCommand.InputGestures.Add(escapeGesture);
        CommandBindings.Add(new CommandBinding(escapeCommand, (s, e) => Close()));
    }

    private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        // Update window title based on content
        if (e.PropertyName == nameof(ComposeEmailViewModel.Subject))
        {
            UpdateWindowTitle();
        }
    }

    private void UpdateWindowTitle()
    {
        if (_viewModel != null)
        {
            var title = "Compose Email";
            if (!string.IsNullOrEmpty(_viewModel.Subject))
            {
                title = $"Compose: {_viewModel.Subject}";
            }
            if (_isDraft)
            {
                title += " (Draft)";
            }
            Title = title;
        }
    }

    private void CcBccButton_Click(object sender, RoutedEventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.ShowCcBcc = !_viewModel.ShowCcBcc;
        }
    }

    private void AttachButton_Click(object sender, RoutedEventArgs e)
    {
        var openFileDialog = new OpenFileDialog
        {
            Title = "Select files to attach",
            Multiselect = true,
            Filter = "All Files (*.*)|*.*"
        };

        if (openFileDialog.ShowDialog() == true)
        {
            foreach (var fileName in openFileDialog.FileNames)
            {
                _viewModel?.AddAttachment(fileName);
            }
        }
    }

    private void SendButton_Click(object sender, RoutedEventArgs e)
    {
        SendEmail();
    }

    private void SaveDraftButton_Click(object sender, RoutedEventArgs e)
    {
        SaveDraft();
    }

    private void DiscardButton_Click(object sender, RoutedEventArgs e)
    {
        if (HasUnsavedChanges())
        {
            var result = MessageBox.Show(
                "Are you sure you want to discard this email?",
                "Discard Email",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Close();
            }
        }
        else
        {
            Close();
        }
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private async void SendEmail()
    {
        if (_viewModel == null) return;

        if (!ValidateEmail())
            return;

        try
        {
            SendButton.IsEnabled = false;
            SendButton.Content = "Sending...";

            var success = await _viewModel.SendEmailAsync();
            if (success)
            {
                MessageBox.Show("Email sent successfully!", "Success", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                Close();
            }
            else
            {
                MessageBox.Show("Failed to send email. Please try again.", "Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error sending email: {ex.Message}", "Error", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            SendButton.IsEnabled = true;
            SendButton.Content = "Send";
        }
    }

    private async void SaveDraft()
    {
        if (_viewModel == null) return;

        try
        {
            var success = await _viewModel.SaveDraftAsync();
            if (success)
            {
                _isDraft = true;
                UpdateWindowTitle();
                // Show brief confirmation
                MessageBox.Show("Draft saved successfully!", "Draft Saved", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving draft: {ex.Message}", "Error", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private bool ValidateEmail()
    {
        if (_viewModel == null) return false;

        if (string.IsNullOrWhiteSpace(_viewModel.ToAddresses))
        {
            MessageBox.Show("Please enter at least one recipient.", "Validation Error", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            ToTextBox.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(_viewModel.Subject))
        {
            var result = MessageBox.Show(
                "Send email without a subject?",
                "No Subject",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.No)
            {
                SubjectTextBox.Focus();
                return false;
            }
        }

        if (string.IsNullOrWhiteSpace(_viewModel.Body))
        {
            var result = MessageBox.Show(
                "Send email with empty body?",
                "Empty Body",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.No)
            {
                BodyTextBox.Focus();
                return false;
            }
        }

        return true;
    }

    private bool HasUnsavedChanges()
    {
        if (_viewModel == null) return false;

        return !string.IsNullOrEmpty(_viewModel.ToAddresses) ||
               !string.IsNullOrEmpty(_viewModel.CcAddresses) ||
               !string.IsNullOrEmpty(_viewModel.BccAddresses) ||
               !string.IsNullOrEmpty(_viewModel.Subject) ||
               !string.IsNullOrEmpty(_viewModel.Body) ||
               _viewModel.Attachments.Count > 0;
    }

    protected override void OnClosing(CancelEventArgs e)
    {
        if (HasUnsavedChanges() && !_isDraft)
        {
            var result = MessageBox.Show(
                "You have unsaved changes. Do you want to save as draft before closing?",
                "Unsaved Changes",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            switch (result)
            {
                case MessageBoxResult.Yes:
                    SaveDraft();
                    break;
                case MessageBoxResult.Cancel:
                    e.Cancel = true;
                    return;
            }
        }

        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }

        base.OnClosing(e);
    }
}
