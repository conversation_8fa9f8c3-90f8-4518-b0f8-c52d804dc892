import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Search, CheckSquare, Clock, User, Edit } from "lucide-react";
import { format } from "date-fns";
import { apiRequest } from "@/lib/api";
import PriorityBadge from "@/components/ui/priority-badge";
import EditTaskModal from "@/components/modals/edit-task-modal";
import UnifiedAiPanel from "@/components/ai/unified-ai-panel";
import { cn } from "@/lib/utils";
import type { Task } from "@shared/schema";

export default function Tasks() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState<"all" | "todo" | "in_progress" | "completed">("all");
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  const queryClient = useQueryClient();

  const { data: tasks, isLoading } = useQuery<Task[]>({
    queryKey: ["/api/tasks"],
    queryFn: () => apiRequest("GET", "/api/tasks").then(res => res.json()),
  });

  const toggleTaskMutation = useMutation({
    mutationFn: (id: number) => apiRequest("POST", `/api/tasks/${id}/complete`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/tasks"] });
    },
  });

  const filteredTasks = tasks?.filter(task => {
    const matchesSearch = !searchQuery || 
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = 
      filter === "all" ||
      (filter === "completed" && task.completed) ||
      (filter === "todo" && !task.completed && task.status === "todo") ||
      (filter === "in_progress" && !task.completed && task.status === "in_progress");
    
    return matchesSearch && matchesFilter;
  }) || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "todo":
        return <Clock className="w-4 h-4 text-gray-500" />;
      case "in_progress":
        return <User className="w-4 h-4 text-blue-500" />;
      case "completed":
        return <CheckSquare className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleEditTask = (task: Task) => {
    setSelectedTask(task);
    setIsEditModalOpen(true);
  };

  const handleTaskToggle = (taskId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    toggleTaskMutation.mutate(taskId);
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Tasks</h2>
            <div className="flex items-center space-x-2">
              <Button
                variant={filter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("all")}
              >
                All
              </Button>
              <Button
                variant={filter === "todo" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("todo")}
              >
                To Do
              </Button>
              <Button
                variant={filter === "in_progress" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("in_progress")}
              >
                In Progress
              </Button>
              <Button
                variant={filter === "completed" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilter("completed")}
              >
                Completed
              </Button>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content - Split View */}
      <div className="flex-1 flex overflow-hidden">
        {/* Tasks List Panel */}
        <div className={`${selectedTask ? 'w-1/3' : 'w-full'} border-r border-gray-200 dark:border-gray-700 overflow-y-auto transition-all duration-300`}>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredTasks.length === 0 ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <CheckSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery ? "No tasks found matching your search" : "No tasks created yet"}
                </p>
              </div>
            </div>
          ) : (
            <div className="p-4 space-y-2">
              {filteredTasks.map((task) => (
                <Card 
                  key={task.id} 
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md group",
                    task.completed && "opacity-60",
                    selectedTask?.id === task.id && "ring-2 ring-blue-500 ring-opacity-50"
                  )}
                  onClick={() => setSelectedTask(task)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      {/* Quick Complete Checkbox */}
                      <Checkbox 
                        checked={task.completed}
                        onCheckedChange={(checked) => handleTaskToggle(task.id, {} as React.MouseEvent)}
                        className="mt-1"
                      />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className={cn(
                              "font-medium text-gray-900 dark:text-white",
                              task.completed && "line-through text-gray-500"
                            )}>
                              {task.title}
                            </h3>
                            {task.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                                {task.description}
                              </p>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2 ml-2">
                            <PriorityBadge priority={task.priority as "low" | "medium" | "high"} />
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditTask(task);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(task.status)}
                            <span className="text-xs text-gray-500 capitalize">
                              {task.status.replace("_", " ")}
                            </span>
                          </div>
                          
                          {task.dueDate && (
                            <span className="text-xs text-gray-500">
                              Due {format(new Date(task.dueDate), "MMM d")}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Task Detail and Context Panel */}
        {selectedTask && (
          <div className="flex-1 flex">
            {/* Task Detail */}
            <div className="flex-1 flex flex-col">
              <div className="border-b bg-white dark:bg-gray-800 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Checkbox 
                      checked={selectedTask.completed}
                      onCheckedChange={(checked) => handleTaskToggle(selectedTask.id, {} as React.MouseEvent)}
                    />
                    <h2 className={cn(
                      "text-xl font-semibold text-gray-900 dark:text-white",
                      selectedTask.completed && "line-through text-gray-500"
                    )}>
                      {selectedTask.title}
                    </h2>
                    <PriorityBadge priority={selectedTask.priority as "low" | "medium" | "high"} />
                  </div>
                  <Button variant="outline" onClick={() => handleEditTask(selectedTask)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>
              
              <div className="flex-1 overflow-y-auto p-6">
                <div className="max-w-2xl space-y-6">
                  {selectedTask.description && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Description
                      </h3>
                      <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {selectedTask.description}
                      </p>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        Status
                      </h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(selectedTask.status)}
                        <span className="text-sm text-gray-700 dark:text-gray-300 capitalize">
                          {selectedTask.completed ? "Completed" : selectedTask.status.replace("_", " ")}
                        </span>
                      </div>
                    </div>
                    
                    {selectedTask.dueDate && (
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                          Due Date
                        </h3>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {format(new Date(selectedTask.dueDate), "EEEE, MMMM d, yyyy")}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                      Created
                    </h3>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {format(new Date(selectedTask.createdAt), "EEEE, MMMM d, yyyy 'at' h:mm a")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* AI Insights Panel */}
            <div className="w-80 bg-gray-50 dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
              <UnifiedAiPanel context="task" itemId={selectedTask.id} />
            </div>
          </div>
        )}
      </div>

      {selectedTask && (
        <EditTaskModal 
          task={selectedTask}
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedTask(null);
          }}
        />
      )}
    </div>
  );
}
