import { useEffect, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";

export function useWebSocket() {
  const wsRef = useRef<WebSocket | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    // Skip WebSocket connection in development if there are issues
    if (process.env.NODE_ENV === 'development') {
      return;
    }
    
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log("WebSocket connected");
      // Authenticate with user ID (in a real app, get this from auth context)
      ws.send(JSON.stringify({
        type: "authenticate",
        userId: 1
      }));
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        
        switch (message.type) {
          case "authenticated":
            console.log("WebSocket authenticated");
            break;
            
          case "new_email":
            toast({
              title: "New Email",
              description: `From: ${message.data.sender}`,
            });
            queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
            break;
            
          case "task_updated":
            toast({
              title: "Task Updated",
              description: message.data.title,
            });
            queryClient.invalidateQueries({ queryKey: ["/api/tasks"] });
            break;
            
          case "ai_insight":
            toast({
              title: "AI Insight",
              description: message.data.title,
            });
            queryClient.invalidateQueries({ queryKey: ["/api/ai-insights"] });
            break;
            
          default:
            console.log("Unknown message type:", message.type);
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };

    ws.onclose = () => {
      console.log("WebSocket disconnected");
    };

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [toast, queryClient]);

  return wsRef.current;
}
