import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { 
  Brain, 
  ThumbsUp, 
  ThumbsDown, 
  CheckSquare, 
  Calendar, 
  Mail, 
  FileText, 
  Users,
  ExternalLink,
  Clock,
  AlertTriangle,
  Lightbulb,
  TrendingUp,
  MapPin,
  Tag
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { apiRequest } from "@/lib/api";
import PriorityBadge from "@/components/ui/priority-badge";
import { cn } from "@/lib/utils";
import type { Task, CalendarEvent, Document, Contact, Email } from "@shared/schema";

interface UnifiedAiPanelProps {
  context: "email" | "task" | "calendar" | "default";
  itemId?: number;
}

export default function UnifiedAiPanel({ context, itemId }: UnifiedAiPanelProps) {
  const queryClient = useQueryClient();

  // Fetch all data sources
  const { data: emails } = useQuery<Email[]>({
    queryKey: ["/api/emails"],
    queryFn: () => apiRequest("GET", "/api/emails").then(res => res.json()),
  });

  const { data: tasks } = useQuery<Task[]>({
    queryKey: ["/api/tasks"],
    queryFn: () => apiRequest("GET", "/api/tasks").then(res => res.json()),
  });

  const { data: events } = useQuery<CalendarEvent[]>({
    queryKey: ["/api/calendar"],
    queryFn: () => apiRequest("GET", "/api/calendar").then(res => res.json()),
  });

  const { data: documents } = useQuery<Document[]>({
    queryKey: ["/api/documents"],
    queryFn: () => apiRequest("GET", "/api/documents").then(res => res.json()),
  });

  const { data: contacts } = useQuery<Contact[]>({
    queryKey: ["/api/contacts"],
    queryFn: () => apiRequest("GET", "/api/contacts").then(res => res.json()),
  });

  const { data: suggestions } = useQuery({
    queryKey: ["/api/ai/generate-suggestions"],
    queryFn: () => apiRequest("POST", "/api/ai/generate-suggestions", {}).then(res => res.json()),
  });

  const { data: aiInsights } = useQuery({
    queryKey: ["/api/ai/analyze-relationships"],
    queryFn: () => apiRequest("POST", "/api/ai/analyze-relationships").then(res => res.json()),
  });

  const feedbackMutation = useMutation({
    mutationFn: ({ type, feedback }: { type: string; feedback: "approve" | "reject" }) =>
      apiRequest("POST", "/api/ai/feedback", { type, feedback }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ai/generate-suggestions"] });
    },
  });

  const handleFeedback = (type: string, feedback: "approve" | "reject") => {
    feedbackMutation.mutate({ type, feedback });
  };

  const getContextualData = () => {
    switch (context) {
      case "email":
        return {
          relatedTasks: tasks?.slice(0, 2) || [],
          relatedEvents: events?.slice(0, 1) || [],
          relatedDocuments: documents?.slice(0, 1) || [],
          relatedContacts: contacts?.slice(0, 2) || [],
        };
      case "task":
        return {
          relatedEmails: emails?.slice(0, 2) || [],
          relatedEvents: events?.slice(0, 1) || [],
          relatedDocuments: documents?.slice(0, 1) || [],
          relatedContacts: contacts?.slice(0, 2) || [],
        };
      case "calendar":
        return {
          relatedEmails: emails?.slice(0, 2) || [],
          relatedTasks: tasks?.slice(0, 1) || [],
          relatedDocuments: documents?.slice(0, 1) || [],
          relatedContacts: contacts?.slice(0, 3) || [],
        };
      default:
        return {
          relatedEmails: emails?.slice(0, 1) || [],
          relatedTasks: tasks?.slice(0, 1) || [],
          relatedEvents: events?.slice(0, 1) || [],
          relatedDocuments: documents?.slice(0, 1) || [],
          relatedContacts: contacts?.slice(0, 1) || [],
        };
    }
  };

  const getContextTitle = () => {
    return "AI Insights";
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "conflict": return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case "follow_up": return <TrendingUp className="w-4 h-4 text-blue-500" />;
      case "meeting_request": return <Calendar className="w-4 h-4 text-purple-500" />;
      case "task": return <CheckSquare className="w-4 h-4 text-green-500" />;
      case "schedule": return <Calendar className="w-4 h-4 text-blue-500" />;
      default: return <Lightbulb className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const contextData = getContextualData();

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <Brain className="w-5 h-5 text-purple-500" />
        <h3 className="font-semibold text-gray-900 dark:text-white">{getContextTitle()}</h3>
      </div>

      {/* Integrated Content - No separate sections */}
      <div className="space-y-3">
        {/* AI Suggestions */}
        {suggestions && suggestions.length > 0 && (
          <>
            {suggestions.slice(0, 3).map((suggestion: any, index: number) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="p-3">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getInsightIcon(suggestion.type)}
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white">
                        {suggestion.title}
                      </h5>
                    </div>
                    <Badge className={getPriorityColor(suggestion.priority)} variant="outline">
                      {suggestion.priority}
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">
                    {suggestion.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-xs">
                      {suggestion.type.replace('_', ' ')}
                    </Badge>
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback(suggestion.type, "approve")}
                        className="p-1 h-auto"
                      >
                        <ThumbsUp className="w-3 h-3 text-green-500" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback(suggestion.type, "reject")}
                        className="p-1 h-auto"
                      >
                        <ThumbsDown className="w-3 h-3 text-red-500" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </>
        )}

        {/* Related Items - Integrated seamlessly */}
        {context !== "default" && (
          <>
            {/* Related Tasks */}
            {contextData.relatedTasks && contextData.relatedTasks.length > 0 && (
              <>
                {contextData.relatedTasks.map((task) => (
                  <Card key={task.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-3">
                      <div className="flex items-start space-x-2">
                        <CheckSquare className="w-4 h-4 text-blue-500 mt-1" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                            {task.title}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <PriorityBadge priority={task.priority as "low" | "medium" | "high"} />
                            {task.dueDate && (
                              <Badge variant="outline" className="text-xs">
                                <Clock className="w-3 h-3 mr-1" />
                                {format(new Date(task.dueDate), "MMM d")}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}

            {/* Related Emails */}
            {contextData.relatedEmails && contextData.relatedEmails.length > 0 && (
              <>
                {contextData.relatedEmails.map((email) => (
                  <Card key={email.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-3">
                      <div className="flex items-start space-x-2">
                        <Mail className="w-4 h-4 text-blue-500 mt-1" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                            {email.subject}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {email.sender}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="w-3 h-3 mr-1" />
                              {format(new Date(email.createdAt), "MMM d")}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}

            {/* Related Events */}
            {contextData.relatedEvents && contextData.relatedEvents.length > 0 && (
              <>
                {contextData.relatedEvents.map((event) => (
                  <Card key={event.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-3">
                      <div className="flex items-start space-x-2">
                        <Calendar className="w-4 h-4 text-purple-500 mt-1" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                            {event.title}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {event.type}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="w-3 h-3 mr-1" />
                              {format(new Date(event.startTime), "MMM d")}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}

            {/* Related Documents */}
            {contextData.relatedDocuments && contextData.relatedDocuments.length > 0 && (
              <>
                {contextData.relatedDocuments.map((doc) => (
                  <Card key={doc.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-3">
                      <div className="flex items-start space-x-2">
                        <FileText className="w-4 h-4 text-green-500 mt-1" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white line-clamp-2">
                            {doc.title}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              <Tag className="w-3 h-3 mr-1" />
                              {doc.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              <Clock className="w-3 h-3 mr-1" />
                              {format(new Date(doc.createdAt), "MMM d")}
                            </Badge>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}

            {/* Related Contacts */}
            {contextData.relatedContacts && contextData.relatedContacts.length > 0 && (
              <>
                {contextData.relatedContacts.map((contact) => (
                  <Card key={contact.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-3">
                      <div className="flex items-start space-x-2">
                        <Users className="w-4 h-4 text-orange-500 mt-1" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {contact.name}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {contact.email}
                            </Badge>
                            {contact.company && (
                              <Badge variant="outline" className="text-xs">
                                {contact.company}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <ExternalLink className="w-3 h-3" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </>
            )}
          </>
        )}

        {/* Context-Specific AI Insights */}
        {context === "calendar" && (
          <Card className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
            <CardContent className="p-3">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-orange-500 mt-1" />
                <div>
                  <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Location Missing
                  </p>
                  <p className="text-xs text-orange-700 dark:text-orange-300">
                    This meeting doesn't have a location set. Consider adding venue details or video call link.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {context === "task" && (
          <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
            <CardContent className="p-3">
              <div className="flex items-start space-x-2">
                <TrendingUp className="w-4 h-4 text-blue-500 mt-1" />
                <div>
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Follow-up Required
                  </p>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    This task may require follow-up communication. Consider scheduling a check-in.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}