import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar, 
  Brain, 
  Clock, 
  MapPin, 
  Users, 
  Zap,
  Mail,
  Plus,
  CheckCircle,
  Car
} from "lucide-react";
import MeetingScheduler from "@/components/ai/meeting-scheduler";
import { useToast } from "@/hooks/use-toast";

// Sample emails that could be meeting requests
const sampleEmails = [
  {
    id: 1,
    subject: "Project Review Meeting",
    sender: "<EMAIL>",
    content: "Hi, I'd like to schedule a meeting to review the Q1 project status. Would sometime next week work for a 1-hour discussion? We could meet in the conference room or via Zoom.",
    priority: "medium" as const,
    suggestedDuration: 60,
    suggestedLocation: "Conference Room A"
  },
  {
    id: 2,
    subject: "Urgent: Client Call Needed",
    sender: "<EMAIL>",
    content: "We need to discuss the contract changes ASAP. Can we have a quick 30-minute call today or tomorrow? This is quite urgent.",
    priority: "high" as const,
    suggestedDuration: 30,
    suggestedLocation: "Virtual/Zoom"
  },
  {
    id: 3,
    subject: "Coffee Chat - New Ideas",
    sender: "<EMAIL>",
    content: "Hey! I have some interesting ideas for collaboration. Would love to grab coffee sometime this week and chat about potential partnerships. No rush on this.",
    priority: "low" as const,
    suggestedDuration: 45,
    suggestedLocation: "Local Coffee Shop"
  }
];

export default function AiScheduler() {
  const [selectedEmail, setSelectedEmail] = useState<number | null>(null);
  const [customEmailContent, setCustomEmailContent] = useState("");
  const [customSenderEmail, setCustomSenderEmail] = useState("");
  const { toast } = useToast();

  const handleMeetingScheduled = (meetingData: any) => {
    toast({
      title: "Meeting scheduled successfully",
      description: `"${meetingData.title}" has been added to your calendar.`,
    });
    setSelectedEmail(null);
    setCustomEmailContent("");
    setCustomSenderEmail("");
  };

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
    }
  };

  const selectedEmailData = selectedEmail ? sampleEmails.find(e => e.id === selectedEmail) : null;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <Brain className="w-7 h-7 mr-3 text-primary" />
          AI Meeting Scheduler
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Intelligent meeting scheduling with travel time calculations and conflict detection
        </p>
      </div>

      {/* Features Overview */}
      <div className="grid md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Brain className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-200">Smart Analysis</h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  AI analyzes emails to suggest optimal meeting times and locations
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Car className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="font-semibold text-green-900 dark:text-green-200">Travel Time</h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Automatic travel time calculations between meeting locations
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="font-semibold text-purple-900 dark:text-purple-200">Conflict Detection</h3>
                <p className="text-sm text-purple-700 dark:text-purple-300">
                  Real-time conflict checking with existing calendar events
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="email-analysis" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="email-analysis" className="flex items-center space-x-2">
            <Mail className="w-4 h-4" />
            <span>From Email</span>
          </TabsTrigger>
          <TabsTrigger value="manual-scheduling" className="flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Manual Scheduling</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email-analysis" className="space-y-6">
          {!selectedEmail ? (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Mail className="w-5 h-5 mr-2" />
                    Sample Meeting Requests
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {sampleEmails.map((email) => (
                      <Card key={email.id} className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                                {email.subject}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                From: {email.sender}
                              </p>
                              <p className="text-sm text-gray-700 dark:text-gray-300 mb-3 line-clamp-2">
                                {email.content}
                              </p>
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className={getPriorityColor(email.priority)}>
                                  {email.priority} priority
                                </Badge>
                                <Badge variant="outline">
                                  <Clock className="w-3 h-3 mr-1" />
                                  {email.suggestedDuration}m
                                </Badge>
                                <Badge variant="outline">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {email.suggestedLocation}
                                </Badge>
                              </div>
                            </div>
                            <Button onClick={() => setSelectedEmail(email.id)}>
                              <Zap className="w-4 h-4 mr-2" />
                              Schedule Meeting
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Custom Email Analysis</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="sender">Sender Email</Label>
                    <Input
                      id="sender"
                      value={customSenderEmail}
                      onChange={(e) => setCustomSenderEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="content">Email Content</Label>
                    <Textarea
                      id="content"
                      value={customEmailContent}
                      onChange={(e) => setCustomEmailContent(e.target.value)}
                      placeholder="Paste email content here for AI analysis..."
                      rows={4}
                    />
                  </div>
                  {customEmailContent.trim() && customSenderEmail.trim() && (
                    <Button onClick={() => setSelectedEmail(-1)}>
                      <Brain className="w-4 h-4 mr-2" />
                      Analyze & Schedule
                    </Button>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <div>
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-lg font-semibold">
                  {selectedEmailData ? `Scheduling: ${selectedEmailData.subject}` : "Analyzing Custom Email"}
                </h2>
                <Button variant="outline" onClick={() => setSelectedEmail(null)}>
                  Back to Emails
                </Button>
              </div>
              
              <MeetingScheduler
                emailContent={selectedEmailData?.content || customEmailContent}
                senderEmail={selectedEmailData?.sender || customSenderEmail}
                initialDuration={selectedEmailData?.suggestedDuration || 30}
                onScheduled={handleMeetingScheduled}
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="manual-scheduling" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Manual Meeting Scheduling
              </CardTitle>
            </CardHeader>
            <CardContent>
              <MeetingScheduler
                onScheduled={handleMeetingScheduled}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}