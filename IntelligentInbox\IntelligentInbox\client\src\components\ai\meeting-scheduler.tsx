import { useState } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Brain, 
  CheckCircle, 
  AlertCircle,
  Car,
  Coffee,
  Video
} from "lucide-react";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { format, addMinutes } from "date-fns";

interface TimeSlot {
  start: Date;
  end: Date;
  duration: number;
  available: boolean;
}

interface MeetingSuggestion {
  suggestedTimes: TimeSlot[];
  meetingDuration: number;
  location?: string;
  priority: 'low' | 'medium' | 'high';
}

interface Conflict {
  eventId: number;
  title: string;
  reason: string;
}

interface MeetingSchedulerProps {
  emailContent?: string;
  senderEmail?: string;
  initialDuration?: number;
  onScheduled?: (meetingData: any) => void;
}

export default function MeetingScheduler({ 
  emailContent, 
  senderEmail, 
  initialDuration = 30,
  onScheduled 
}: MeetingSchedulerProps) {
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [customDuration, setCustomDuration] = useState(initialDuration);
  const [customLocation, setCustomLocation] = useState("");
  const [meetingTitle, setMeetingTitle] = useState("");
  const [attendees, setAttendees] = useState(senderEmail || "");
  const [conflicts, setConflicts] = useState<Conflict[]>([]);
  const { toast } = useToast();

  // Get AI meeting suggestions from email content
  const { data: aiSuggestions, isLoading: loadingSuggestions, refetch: suggestTimes } = useQuery({
    queryKey: ["/api/ai/suggest-meeting-times", emailContent],
    enabled: !!emailContent,
    queryFn: () => apiRequest("POST", "/api/ai/suggest-meeting-times", {
      emailContent,
      senderEmail,
      duration: customDuration
    })
  });

  // Find available slots manually
  const findSlotsMutation = useMutation({
    mutationFn: (data: { duration: number; location?: string; buffer?: number; searchDays?: number }) =>
      apiRequest("POST", "/api/ai/find-available-slots", data),
    onSuccess: (data) => {
      toast({
        title: "Available slots found",
        description: `Found ${data.availableSlots.length} available time slots.`,
      });
    }
  });

  // Check for conflicts
  const checkConflictsMutation = useMutation({
    mutationFn: (data: { startTime: Date; endTime: Date; location?: string }) =>
      apiRequest("POST", "/api/calendar/check-conflicts", {
        startTime: data.startTime.toISOString(),
        endTime: data.endTime.toISOString(),
        location: data.location
      }),
    onSuccess: (data) => {
      setConflicts(data.conflicts || []);
      if (data.hasConflict) {
        toast({
          title: "Scheduling conflict detected",
          description: `Found ${data.conflicts.length} conflict(s) with existing events.`,
          variant: "destructive"
        });
      }
    }
  });

  // Create calendar event
  const createEventMutation = useMutation({
    mutationFn: (eventData: any) => apiRequest("POST", "/api/calendar", eventData),
    onSuccess: (event) => {
      toast({
        title: "Meeting scheduled",
        description: "The meeting has been added to your calendar.",
      });
      onScheduled?.(event);
      setSelectedSlot(null);
      setConflicts([]);
    }
  });

  const handleSlotSelect = (slot: TimeSlot) => {
    setSelectedSlot(slot);
    
    // Check for conflicts when a slot is selected
    const endTime = addMinutes(new Date(slot.start), customDuration);
    checkConflictsMutation.mutate({
      startTime: new Date(slot.start),
      endTime,
      location: customLocation || slot.location
    });
  };

  const handleFindSlots = () => {
    findSlotsMutation.mutate({
      duration: customDuration,
      location: customLocation,
      buffer: 15,
      searchDays: 14
    });
  };

  const handleScheduleMeeting = () => {
    if (!selectedSlot || !meetingTitle.trim()) {
      toast({
        title: "Missing information",
        description: "Please select a time slot and enter a meeting title.",
        variant: "destructive"
      });
      return;
    }

    const eventData = {
      title: meetingTitle,
      startTime: new Date(selectedSlot.start).toISOString(),
      endTime: addMinutes(new Date(selectedSlot.start), customDuration).toISOString(),
      location: customLocation,
      attendees: attendees.split(',').map(email => email.trim()).filter(Boolean),
      description: emailContent ? `Generated from email conversation with ${senderEmail}` : undefined
    };

    createEventMutation.mutate(eventData);
  };

  const getPriorityColor = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
    }
  };

  const getLocationIcon = (location?: string) => {
    if (!location) return <MapPin className="w-4 h-4" />;
    const loc = location.toLowerCase();
    if (loc.includes('zoom') || loc.includes('virtual') || loc.includes('online')) {
      return <Video className="w-4 h-4" />;
    }
    if (loc.includes('coffee') || loc.includes('cafe')) {
      return <Coffee className="w-4 h-4" />;
    }
    return <MapPin className="w-4 h-4" />;
  };

  const availableSlots = aiSuggestions?.suggestedTimes || findSlotsMutation.data?.availableSlots || [];

  return (
    <div className="space-y-6">
      {/* AI Meeting Analysis */}
      {emailContent && aiSuggestions && (
        <Card className="bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <Brain className="w-5 h-5 text-blue-600 mt-1" />
              <div className="flex-1">
                <h4 className="font-medium text-blue-900 dark:text-blue-200">
                  AI Meeting Analysis
                </h4>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span className="text-sm text-blue-800 dark:text-blue-300">
                      {aiSuggestions.meetingDuration} minutes
                    </span>
                  </div>
                  {aiSuggestions.location && (
                    <div className="flex items-center space-x-1">
                      {getLocationIcon(aiSuggestions.location)}
                      <span className="text-sm text-blue-800 dark:text-blue-300">
                        {aiSuggestions.location}
                      </span>
                    </div>
                  )}
                  <Badge variant="outline" className={getPriorityColor(aiSuggestions.priority)}>
                    {aiSuggestions.priority} priority
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Meeting Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Meeting Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Meeting Title</Label>
              <Input
                id="title"
                value={meetingTitle}
                onChange={(e) => setMeetingTitle(e.target.value)}
                placeholder="Enter meeting title"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Select value={customDuration.toString()} onValueChange={(value) => setCustomDuration(parseInt(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 minutes</SelectItem>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="45">45 minutes</SelectItem>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="90">1.5 hours</SelectItem>
                  <SelectItem value="120">2 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={customLocation}
                onChange={(e) => setCustomLocation(e.target.value)}
                placeholder="Meeting location or 'Virtual/Zoom'"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="attendees">Attendees (emails, comma-separated)</Label>
              <Input
                id="attendees"
                value={attendees}
                onChange={(e) => setAttendees(e.target.value)}
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>
          </div>

          {!emailContent && (
            <Button onClick={handleFindSlots} disabled={findSlotsMutation.isPending}>
              <Clock className="w-4 h-4 mr-2" />
              {findSlotsMutation.isPending ? "Finding..." : "Find Available Times"}
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Available Time Slots */}
      {availableSlots.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                Available Time Slots
              </span>
              <Badge variant="outline">
                {availableSlots.length} slots found
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 max-h-64 overflow-y-auto">
              {availableSlots.map((slot, index) => (
                <Button
                  key={index}
                  variant={selectedSlot === slot ? "default" : "outline"}
                  className="justify-start h-auto p-3"
                  onClick={() => handleSlotSelect(slot)}
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-4 h-4" />
                      <div className="text-left">
                        <div className="font-medium">
                          {format(new Date(slot.start), 'EEEE, MMM d')}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {format(new Date(slot.start), 'h:mm a')} - {format(new Date(slot.end), 'h:mm a')}
                        </div>
                      </div>
                    </div>
                    {selectedSlot === slot && (
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    )}
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Conflicts Warning */}
      {conflicts.length > 0 && (
        <Card className="border-red-200 dark:border-red-800">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-600 mt-1" />
              <div>
                <h4 className="font-medium text-red-900 dark:text-red-200">
                  Scheduling Conflicts Detected
                </h4>
                <div className="mt-2 space-y-1">
                  {conflicts.map((conflict, index) => (
                    <div key={index} className="text-sm text-red-800 dark:text-red-300">
                      • {conflict.title} ({conflict.reason})
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Schedule Button */}
      {selectedSlot && (
        <Card className="bg-green-50 dark:bg-green-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-green-900 dark:text-green-200">
                  Ready to Schedule
                </h4>
                <p className="text-sm text-green-800 dark:text-green-300 mt-1">
                  {meetingTitle || "Untitled Meeting"} • {format(new Date(selectedSlot.start), 'MMM d, h:mm a')}
                  {customLocation && ` • ${customLocation}`}
                </p>
              </div>
              <Button 
                onClick={handleScheduleMeeting}
                disabled={createEventMutation.isPending || !meetingTitle.trim()}
                className="bg-green-600 hover:bg-green-700"
              >
                {createEventMutation.isPending ? "Scheduling..." : "Schedule Meeting"}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}