<UserControl x:Class="EmailClient.Views.Controls.ModernSidebar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="280">
    
    <Border Background="{StaticResource SidebarBrush}" 
            BorderBrush="{StaticResource SidebarBorderBrush}" 
            BorderThickness="0,0,1,0">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Brand Header -->
            <Border Grid.Row="0" Padding="24,20">
                <StackPanel Orientation="Horizontal">
                    <Border Width="32" Height="32" 
                            Background="{StaticResource PrimaryBrush}" 
                            CornerRadius="8"
                            Margin="0,0,12,0">
                        <TextBlock Text="📧" 
                                   FontSize="16" 
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center"/>
                    </Border>
                    <TextBlock Text="IntelInbox" 
                               Style="{StaticResource HeadingMediumStyle}"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Account Switcher Section -->
            <Border Grid.Row="1" 
                    Padding="16,0,16,16" 
                    BorderBrush="{StaticResource BorderBrush}" 
                    BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" 
                               Text="ACCOUNTS" 
                               Style="{StaticResource CaptionTextStyle}"
                               FontWeight="SemiBold"
                               Margin="0,0,0,8"/>
                    
                    <!-- Account Switcher -->
                    <Button Grid.Row="1" 
                            x:Name="AccountSwitcherButton"
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Padding="12,8"
                            Click="AccountSwitcherButton_Click">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" 
                                    Width="8" Height="8" 
                                    Background="{StaticResource SuccessBrush}" 
                                    CornerRadius="4"
                                    Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock x:Name="AccountNameText"
                                           Text="Unified Inbox" 
                                           Style="{StaticResource BodyTextStyle}"
                                           FontWeight="Medium"/>
                                <TextBlock x:Name="AccountInfoText"
                                           Text="All accounts • 3 connected" 
                                           Style="{StaticResource CaptionTextStyle}"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Column="2" 
                                       Text="⌄" 
                                       Style="{StaticResource CaptionTextStyle}"
                                       VerticalAlignment="Center"/>
                        </Grid>
                    </Button>
                </Grid>
            </Border>
            
            <!-- Navigation Menu -->
            <ScrollViewer Grid.Row="2" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="16,16,16,0">
                <StackPanel x:Name="NavigationPanel">
                    <!-- Main Navigation -->
                    <StackPanel Margin="0,0,0,16">
                        <!-- Inbox -->
                        <Button x:Name="InboxButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Background="{StaticResource SidebarItemActiveBrush}"
                                Foreground="White"
                                Click="NavigationButton_Click"
                                Tag="Inbox">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="📧" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="Inbox" Style="{StaticResource BodyTextStyle}" Foreground="White"/>
                                <Border Grid.Column="2" 
                                        x:Name="InboxBadge"
                                        Background="White" 
                                        CornerRadius="10" 
                                        Padding="6,2"
                                        MinWidth="20">
                                    <TextBlock Text="24" 
                                               FontSize="11" 
                                               FontWeight="Medium"
                                               Foreground="{StaticResource PrimaryBrush}"
                                               HorizontalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Button>
                        
                        <!-- Contacts -->
                        <Button x:Name="ContactsButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="Contacts">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="👥" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="Contacts" Style="{StaticResource BodyTextStyle}"/>
                            </Grid>
                        </Button>
                        
                        <!-- Tasks -->
                        <Button x:Name="TasksButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="Tasks">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="✓" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="Tasks" Style="{StaticResource BodyTextStyle}"/>
                                <Border Grid.Column="2" 
                                        Background="{StaticResource BorderBrush}" 
                                        CornerRadius="10" 
                                        Padding="6,2"
                                        MinWidth="20">
                                    <TextBlock Text="12" 
                                               FontSize="11" 
                                               FontWeight="Medium"
                                               Foreground="{StaticResource TextSecondaryBrush}"
                                               HorizontalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </Button>
                        
                        <!-- Calendar -->
                        <Button x:Name="CalendarButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="Calendar">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="📅" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="Calendar" Style="{StaticResource BodyTextStyle}"/>
                            </Grid>
                        </Button>
                        
                        <!-- Documents -->
                        <Button x:Name="DocumentsButton"
                                Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="Documents">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="📄" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="Documents" Style="{StaticResource BodyTextStyle}"/>
                            </Grid>
                        </Button>
                    </StackPanel>
                    
                    <!-- AI Assistant Section -->
                    <StackPanel Margin="0,0,0,16">
                        <TextBlock Text="AI ASSISTANT" 
                                   Style="{StaticResource CaptionTextStyle}"
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,8"/>
                        
                        <Button Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="AIInsights">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Border Grid.Column="0" 
                                        Width="16" Height="16" 
                                        Background="{StaticResource PrimaryBrush}" 
                                        CornerRadius="8"
                                        Margin="0,0,12,0">
                                    <TextBlock Text="✨" FontSize="10" 
                                               HorizontalAlignment="Center" 
                                               VerticalAlignment="Center"
                                               Foreground="White"/>
                                </Border>
                                <TextBlock Grid.Column="1" Text="Smart Insights" Style="{StaticResource BodyTextStyle}"/>
                            </Grid>
                        </Button>
                        
                        <Button Style="{StaticResource ModernIconButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Margin="0,2"
                                Click="NavigationButton_Click"
                                Tag="AIAutomations">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="⚡" FontSize="16" Margin="0,0,12,0"/>
                                <TextBlock Grid.Column="1" Text="AI Automations" Style="{StaticResource BodyTextStyle}"/>
                            </Grid>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
            
            <!-- User Profile & Settings -->
            <Border Grid.Row="3" 
                    Padding="16" 
                    BorderBrush="{StaticResource BorderBrush}" 
                    BorderThickness="0,1,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <Border Grid.Column="0" 
                            Width="40" Height="40" 
                            Background="{StaticResource BorderBrush}" 
                            CornerRadius="20"
                            Margin="0,0,12,0">
                        <TextBlock Text="JS" 
                                   Style="{StaticResource BodyTextStyle}"
                                   FontWeight="Medium"
                                   HorizontalAlignment="Center" 
                                   VerticalAlignment="Center"/>
                    </Border>
                    
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="John Smith" 
                                   Style="{StaticResource BodyTextStyle}"
                                   FontWeight="Medium"/>
                        <TextBlock Text="<EMAIL>" 
                                   Style="{StaticResource CaptionTextStyle}"/>
                    </StackPanel>
                    
                    <Button Grid.Column="2" 
                            x:Name="SettingsButton"
                            Style="{StaticResource ModernIconButtonStyle}"
                            Click="SettingsButton_Click">
                        <TextBlock Text="⚙" FontSize="16"/>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
