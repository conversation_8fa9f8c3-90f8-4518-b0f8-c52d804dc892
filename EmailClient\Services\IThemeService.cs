using System.ComponentModel;

namespace EmailClient.Services;

public interface IThemeService : INotifyPropertyChanged
{
    /// <summary>
    /// Gets the current theme
    /// </summary>
    AppTheme CurrentTheme { get; }
    
    /// <summary>
    /// Gets whether dark mode is currently active
    /// </summary>
    bool IsDarkMode { get; }
    
    /// <summary>
    /// Sets the application theme
    /// </summary>
    /// <param name="theme">The theme to apply</param>
    void SetTheme(AppTheme theme);
    
    /// <summary>
    /// Toggles between light and dark themes
    /// </summary>
    void ToggleTheme();
    
    /// <summary>
    /// Applies the system theme (follows Windows theme setting)
    /// </summary>
    void ApplySystemTheme();
    
    /// <summary>
    /// Gets whether the system is using dark mode
    /// </summary>
    bool IsSystemDarkMode();
    
    /// <summary>
    /// Event fired when theme changes
    /// </summary>
    event EventHandler<ThemeChangedEventArgs>? ThemeChanged;
}

public enum AppTheme
{
    Light,
    Dark,
    System
}

public class ThemeChangedEventArgs : EventArgs
{
    public AppTheme PreviousTheme { get; }
    public AppTheme NewTheme { get; }
    
    public ThemeChangedEventArgs(AppTheme previousTheme, AppTheme newTheme)
    {
        PreviousTheme = previousTheme;
        NewTheme = newTheme;
    }
}
