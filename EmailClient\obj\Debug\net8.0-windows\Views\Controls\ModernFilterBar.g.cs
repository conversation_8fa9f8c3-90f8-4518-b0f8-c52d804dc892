﻿#pragma checksum "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "15BC7FB9FF81CD5010FF0977053E90E424F8A2FA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EmailClient.Views.Controls {
    
    
    /// <summary>
    /// ModernFilterBar
    /// </summary>
    public partial class ModernFilterBar : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 11 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AllFilterButton;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UnreadFilterButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportantFilterButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachmentsFilterButton;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FilterCountBadge;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilterCountText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EmailClient;V1.0.0.0;component/views/controls/modernfilterbar.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AllFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 17 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
            this.AllFilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.UnreadFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
            this.UnreadFilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ImportantFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
            this.ImportantFilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AttachmentsFilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\..\Views\Controls\ModernFilterBar.xaml"
            this.AttachmentsFilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FilterCountBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.FilterCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

