import { WebSocketServer, WebSocket } from "ws";
import { Server } from "http";

interface WebSocketMessage {
  type: string;
  data: any;
  userId: number;
}

interface ConnectedClient {
  ws: WebSocket;
  userId: number;
}

export class WebSocketService {
  private wss: WebSocketServer;
  private clients: Map<number, ConnectedClient[]> = new Map();

  constructor(server: Server) {
    this.wss = new WebSocketServer({ server, path: '/ws' });
    this.setupWebSocket();
  }

  private setupWebSocket() {
    this.wss.on('connection', (ws: WebSocket) => {
      let userId: number | null = null;

      ws.on('message', (message: string) => {
        try {
          const data: WebSocketMessage = JSON.parse(message);
          
          if (data.type === 'authenticate') {
            userId = data.userId;
            this.addClient(userId, ws);
            this.sendToClient(userId, {
              type: 'authenticated',
              data: { success: true }
            });
          }
        } catch (error) {
          console.error('WebSocket message error:', error);
        }
      });

      ws.on('close', () => {
        if (userId) {
          this.removeClient(userId, ws);
        }
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  private addClient(userId: number, ws: WebSocket) {
    if (!this.clients.has(userId)) {
      this.clients.set(userId, []);
    }
    this.clients.get(userId)!.push({ ws, userId });
  }

  private removeClient(userId: number, ws: WebSocket) {
    const userClients = this.clients.get(userId);
    if (userClients) {
      const index = userClients.findIndex(client => client.ws === ws);
      if (index > -1) {
        userClients.splice(index, 1);
      }
      if (userClients.length === 0) {
        this.clients.delete(userId);
      }
    }
  }

  public sendToClient(userId: number, message: any) {
    const userClients = this.clients.get(userId);
    if (userClients) {
      userClients.forEach(client => {
        if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.send(JSON.stringify(message));
        }
      });
    }
  }

  public broadcast(message: any) {
    this.clients.forEach((clients) => {
      clients.forEach(client => {
        if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.send(JSON.stringify(message));
        }
      });
    });
  }

  public notifyNewEmail(userId: number, email: any) {
    this.sendToClient(userId, {
      type: 'new_email',
      data: email
    });
  }

  public notifyTaskUpdate(userId: number, task: any) {
    this.sendToClient(userId, {
      type: 'task_updated',
      data: task
    });
  }

  public notifyAiInsight(userId: number, insight: any) {
    this.sendToClient(userId, {
      type: 'ai_insight',
      data: insight
    });
  }
}
