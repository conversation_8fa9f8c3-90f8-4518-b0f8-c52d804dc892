import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Send, 
  Paperclip, 
  Image, 
  Smile, 
  Bold, 
  Italic, 
  Underline,
  AlignLeft,
  AlignCenter,
  List,
  X,
  Minimize2,
  Maximize2
} from "lucide-react";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface ComposeModalProps {
  isOpen: boolean;
  onClose: () => void;
  replyTo?: {
    to: string;
    subject: string;
    body?: string;
  };
}

export default function ComposeModal({ isOpen, onClose, replyTo }: ComposeModalProps) {
  const [isMinimized, setIsMinimized] = useState(false);
  const [formData, setFormData] = useState({
    to: replyTo?.to || "",
    cc: "",
    bcc: "",
    subject: replyTo?.subject || "",
    body: replyTo?.body || "",
    priority: "medium" as "low" | "medium" | "high",
  });
  const [showCcBcc, setShowCcBcc] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const sendEmailMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/emails", {
      ...data,
      sender: "<EMAIL>", // This would come from user session
      isRead: true,
      isStarred: false,
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
      toast({ title: "Email sent successfully" });
      onClose();
      resetForm();
    },
    onError: () => {
      toast({ title: "Failed to send email", variant: "destructive" });
    },
  });

  const resetForm = () => {
    setFormData({
      to: "",
      cc: "",
      bcc: "",
      subject: "",
      body: "",
      priority: "medium",
    });
    setShowCcBcc(false);
  };

  const handleSend = () => {
    if (!formData.to.trim() || !formData.subject.trim() || !formData.body.trim()) {
      toast({ title: "Please fill in all required fields", variant: "destructive" });
      return;
    }

    sendEmailMutation.mutate(formData);
  };

  const handleSaveDraft = () => {
    // Could implement draft saving functionality
    toast({ title: "Draft saved" });
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg w-80 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Compose Email</span>
              {formData.to && (
                <span className="text-xs text-gray-500 truncate">to: {formData.to}</span>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setIsMinimized(false)}
              >
                <Maximize2 className="w-3 h-3" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <DialogTitle>Compose Email</DialogTitle>
          <div className="flex items-center space-x-2">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setIsMinimized(true)}
            >
              <Minimize2 className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-col h-[600px]">
          {/* Recipients */}
          <div className="p-4 space-y-3 border-b">
            <div className="flex items-center space-x-2">
              <Label htmlFor="to" className="w-12 text-right">To:</Label>
              <Input
                id="to"
                value={formData.to}
                onChange={(e) => setFormData({ ...formData, to: e.target.value })}
                placeholder="<EMAIL>"
                className="flex-1"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCcBcc(!showCcBcc)}
                className="text-xs"
              >
                Cc/Bcc
              </Button>
            </div>

            {showCcBcc && (
              <>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="cc" className="w-12 text-right">Cc:</Label>
                  <Input
                    id="cc"
                    value={formData.cc}
                    onChange={(e) => setFormData({ ...formData, cc: e.target.value })}
                    placeholder="<EMAIL>"
                    className="flex-1"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Label htmlFor="bcc" className="w-12 text-right">Bcc:</Label>
                  <Input
                    id="bcc"
                    value={formData.bcc}
                    onChange={(e) => setFormData({ ...formData, bcc: e.target.value })}
                    placeholder="<EMAIL>"
                    className="flex-1"
                  />
                </div>
              </>
            )}

            <div className="flex items-center space-x-2">
              <Label htmlFor="subject" className="w-12 text-right">Subject:</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="Email subject"
                className="flex-1"
              />
              <Select
                value={formData.priority}
                onValueChange={(value) => setFormData({ ...formData, priority: value as any })}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low Priority</SelectItem>
                  <SelectItem value="medium">Normal</SelectItem>
                  <SelectItem value="high">High Priority</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Formatting Toolbar */}
          <div className="p-2 border-b bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center space-x-1">
              <Button variant="ghost" size="sm">
                <Bold className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Italic className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Underline className="w-4 h-4" />
              </Button>
              <div className="w-px h-6 bg-gray-300 mx-2" />
              <Button variant="ghost" size="sm">
                <AlignLeft className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <AlignCenter className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <List className="w-4 h-4" />
              </Button>
              <div className="w-px h-6 bg-gray-300 mx-2" />
              <Button variant="ghost" size="sm">
                <Paperclip className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Image className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Smile className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Email Body */}
          <div className="flex-1 p-4">
            <Textarea
              value={formData.body}
              onChange={(e) => setFormData({ ...formData, body: e.target.value })}
              placeholder="Write your email..."
              className="w-full h-full resize-none border-none focus:ring-0 text-base"
            />
          </div>

          {/* Footer */}
          <div className="p-4 border-t bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button 
                  onClick={handleSend}
                  disabled={sendEmailMutation.isPending}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Send className="w-4 h-4 mr-2" />
                  {sendEmailMutation.isPending ? "Sending..." : "Send"}
                </Button>
                <Button variant="outline" onClick={handleSaveDraft}>
                  Save Draft
                </Button>
              </div>
              
              <div className="text-xs text-gray-500">
                Press Ctrl+Enter to send
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}