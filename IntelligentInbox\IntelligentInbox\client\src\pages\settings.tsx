import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Clock, 
  Calendar, 
  Save, 
  User, 
  Bell, 
  Shield,
  Palette,
  Globe,
  Car,
  CheckCircle
} from "lucide-react";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface UserSettings {
  id: number;
  username: string;
  email: string;
  defaultLocation: string;
  workingHours: {
    start: string;
    end: string;
    timezone: string;
    workDays: number[];
  };
}

const timezones = [
  { value: "America/New_York", label: "Eastern Time (ET)" },
  { value: "America/Chicago", label: "Central Time (CT)" },
  { value: "America/Denver", label: "Mountain Time (MT)" },
  { value: "America/Los_Angeles", label: "Pacific Time (PT)" },
  { value: "Europe/London", label: "Greenwich Mean Time (GMT)" },
  { value: "Europe/Paris", label: "Central European Time (CET)" },
  { value: "Asia/Tokyo", label: "Japan Standard Time (JST)" },
  { value: "Asia/Shanghai", label: "China Standard Time (CST)" },
];

const weekDays = [
  { value: 0, label: "Sunday", short: "Sun" },
  { value: 1, label: "Monday", short: "Mon" },
  { value: 2, label: "Tuesday", short: "Tue" },
  { value: 3, label: "Wednesday", short: "Wed" },
  { value: 4, label: "Thursday", short: "Thu" },
  { value: 5, label: "Friday", short: "Fri" },
  { value: 6, label: "Saturday", short: "Sat" },
];

export default function Settings() {
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: settings, isLoading } = useQuery({
    queryKey: ["/api/user/settings"],
  });

  const [localSettings, setLocalSettings] = useState<UserSettings>({
    id: 1,
    username: "",
    email: "",
    defaultLocation: "",
    workingHours: {
      start: "09:00",
      end: "17:00",
      timezone: "America/New_York",
      workDays: [1, 2, 3, 4, 5]
    }
  });

  // Update local settings when server data loads
  useState(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  const updateMutation = useMutation({
    mutationFn: (data: Partial<UserSettings>) => 
      apiRequest("PATCH", "/api/user/settings", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/user/settings"] });
      setHasChanges(false);
      toast({
        title: "Settings saved",
        description: "Your preferences have been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error saving settings",
        description: "Failed to update your preferences. Please try again.",
        variant: "destructive"
      });
    }
  });

  const processTravelTimesMutation = useMutation({
    mutationFn: () => apiRequest("POST", "/api/calendar/process-travel-times"),
    onSuccess: () => {
      toast({
        title: "Travel times updated",
        description: "All calendar events have been processed for travel time calculations.",
      });
    }
  });

  const updateField = (field: string, value: any) => {
    if (field.startsWith('workingHours.')) {
      const subField = field.split('.')[1];
      setLocalSettings(prev => ({
        ...prev,
        workingHours: {
          ...prev.workingHours,
          [subField]: value
        }
      }));
    } else {
      setLocalSettings(prev => ({
        ...prev,
        [field]: value
      }));
    }
    setHasChanges(true);
  };

  const toggleWorkDay = (dayValue: number) => {
    const newWorkDays = localSettings.workingHours.workDays.includes(dayValue)
      ? localSettings.workingHours.workDays.filter(d => d !== dayValue)
      : [...localSettings.workingHours.workDays, dayValue].sort();
    
    updateField('workingHours.workDays', newWorkDays);
  };

  const saveSettings = () => {
    updateMutation.mutate(localSettings);
  };

  const processAllTravelTimes = () => {
    processTravelTimesMutation.mutate();
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your preferences and scheduling settings
          </p>
        </div>
        {hasChanges && (
          <Button onClick={saveSettings} disabled={updateMutation.isPending}>
            <Save className="w-4 h-4 mr-2" />
            {updateMutation.isPending ? "Saving..." : "Save Changes"}
          </Button>
        )}
      </div>

      {/* Profile Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={localSettings.username}
                onChange={(e) => updateField('username', e.target.value)}
                placeholder="Your username"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={localSettings.email}
                onChange={(e) => updateField('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location & Travel Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            Location & Travel Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="defaultLocation">Default Location</Label>
            <Input
              id="defaultLocation"
              value={localSettings.defaultLocation}
              onChange={(e) => updateField('defaultLocation', e.target.value)}
              placeholder="123 Main St, City, State or Your Office Address"
            />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              This location is used to calculate travel times to and from your meetings
            </p>
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-base">Auto-calculate Travel Times</Label>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Automatically add travel time to calendar events based on location
                </p>
              </div>
              <Button 
                variant="outline" 
                onClick={processAllTravelTimes}
                disabled={processTravelTimesMutation.isPending}
              >
                <Car className="w-4 h-4 mr-2" />
                {processTravelTimesMutation.isPending ? "Processing..." : "Process All Events"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Working Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Working Hours & Availability
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startTime">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={localSettings.workingHours.start}
                onChange={(e) => updateField('workingHours.start', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endTime">End Time</Label>
              <Input
                id="endTime"
                type="time"
                value={localSettings.workingHours.end}
                onChange={(e) => updateField('workingHours.end', e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select 
                value={localSettings.workingHours.timezone} 
                onValueChange={(value) => updateField('workingHours.timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <Label>Working Days</Label>
            <div className="flex flex-wrap gap-2">
              {weekDays.map((day) => (
                <Button
                  key={day.value}
                  variant={localSettings.workingHours.workDays.includes(day.value) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleWorkDay(day.value)}
                  className="min-w-[60px]"
                >
                  {localSettings.workingHours.workDays.includes(day.value) && (
                    <CheckCircle className="w-3 h-3 mr-1" />
                  )}
                  {day.short}
                </Button>
              ))}
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Select the days you're available for meetings
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Scheduling Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Smart Scheduling Preferences
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Auto-suggest Meeting Times</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    AI suggests optimal meeting times from emails
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Buffer Time Between Meetings</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Add automatic buffer for transitions
                  </p>
                </div>
                <Select defaultValue="15">
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0m</SelectItem>
                    <SelectItem value="5">5m</SelectItem>
                    <SelectItem value="15">15m</SelectItem>
                    <SelectItem value="30">30m</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Include Travel Time</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Block travel time in calendar
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label>Default Meeting Duration</Label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    When AI suggests meetings
                  </p>
                </div>
                <Select defaultValue="30">
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15m</SelectItem>
                    <SelectItem value="30">30m</SelectItem>
                    <SelectItem value="60">60m</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      {localSettings.defaultLocation && localSettings.workingHours.start && (
        <Card className="bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 dark:text-blue-200">
                  Smart Scheduling Ready
                </h4>
                <p className="text-sm text-blue-800 dark:text-blue-300 mt-1">
                  Your location and working hours are configured. AI can now suggest optimal meeting times 
                  and automatically calculate travel times for your calendar events.
                </p>
                <div className="flex flex-wrap gap-2 mt-2">
                  <Badge variant="outline" className="text-blue-700 border-blue-300">
                    <MapPin className="w-3 h-3 mr-1" />
                    {localSettings.defaultLocation}
                  </Badge>
                  <Badge variant="outline" className="text-blue-700 border-blue-300">
                    <Clock className="w-3 h-3 mr-1" />
                    {localSettings.workingHours.start} - {localSettings.workingHours.end}
                  </Badge>
                  <Badge variant="outline" className="text-blue-700 border-blue-300">
                    <Globe className="w-3 h-3 mr-1" />
                    {timezones.find(tz => tz.value === localSettings.workingHours.timezone)?.label}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}