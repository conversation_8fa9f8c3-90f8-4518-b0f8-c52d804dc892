<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    
    <!-- Modern Icon Definitions using Unicode symbols and Segoe MDL2 Assets -->
    
    <!-- Email Icons -->
    <system:String x:Key="Icon.Email">📧</system:String>
    <system:String x:Key="Icon.EmailOpen">📬</system:String>
    <system:String x:Key="Icon.EmailUnread">📩</system:String>
    <system:String x:Key="Icon.Compose">✉️</system:String>
    <system:String x:Key="Icon.Send">📤</system:String>
    <system:String x:Key="Icon.Reply">↩️</system:String>
    <system:String x:Key="Icon.ReplyAll">↩️↩️</system:String>
    <system:String x:Key="Icon.Forward">➡️</system:String>
    <system:String x:Key="Icon.Archive">📦</system:String>
    <system:String x:Key="Icon.Delete">🗑️</system:String>
    <system:String x:Key="Icon.Star">⭐</system:String>
    <system:String x:Key="Icon.StarOutline">☆</system:String>
    <system:String x:Key="Icon.Important">❗</system:String>
    <system:String x:Key="Icon.Attachment">📎</system:String>
    <system:String x:Key="Icon.Draft">📝</system:String>
    
    <!-- Navigation Icons -->
    <system:String x:Key="Icon.Inbox">📥</system:String>
    <system:String x:Key="Icon.Sent">📤</system:String>
    <system:String x:Key="Icon.Drafts">📝</system:String>
    <system:String x:Key="Icon.Trash">🗑️</system:String>
    <system:String x:Key="Icon.Spam">🚫</system:String>
    <system:String x:Key="Icon.Folder">📁</system:String>
    <system:String x:Key="Icon.FolderOpen">📂</system:String>

    <!-- Account Icons -->
    <system:String x:Key="Icon.Account">👤</system:String>
    <system:String x:Key="Icon.Accounts">👥</system:String>
    <system:String x:Key="Icon.AddAccount">➕</system:String>
    <system:String x:Key="Icon.Gmail">📧</system:String>
    <system:String x:Key="Icon.Outlook">📮</system:String>
    <system:String x:Key="Icon.Yahoo">💌</system:String>
    <system:String x:Key="Icon.Exchange">🏢</system:String>
    <system:String x:Key="Icon.Generic">📬</system:String>

    <!-- Status Icons -->
    <system:String x:Key="Icon.Online">🟢</system:String>
    <system:String x:Key="Icon.Offline">🔴</system:String>
    <system:String x:Key="Icon.Syncing">🔄</system:String>
    <system:String x:Key="Icon.Error">❌</system:String>
    <system:String x:Key="Icon.Warning">⚠️</system:String>
    <system:String x:Key="Icon.Success">✅</system:String>
    <system:String x:Key="Icon.Info">ℹ️</system:String>
    
    <!-- Action Icons -->
    <system:String x:Key="Icon.Search">🔍</system:String>
    <system:String x:Key="Icon.Filter">🔽</system:String>
    <system:String x:Key="Icon.Sort">↕️</system:String>
    <system:String x:Key="Icon.Refresh">🔄</system:String>
    <system:String x:Key="Icon.Settings">⚙️</system:String>
    <system:String x:Key="Icon.Close">✕</system:String>
    <system:String x:Key="Icon.Minimize">🗕</system:String>
    <system:String x:Key="Icon.Maximize">🗖</system:String>
    <system:String x:Key="Icon.Restore">🗗</system:String>
    <system:String x:Key="Icon.Menu">☰</system:String>
    <system:String x:Key="Icon.More">⋯</system:String>
    <system:String x:Key="Icon.MoreVertical">⋮</system:String>

    <!-- Theme Icons -->
    <system:String x:Key="Icon.LightMode">☀️</system:String>
    <system:String x:Key="Icon.DarkMode">🌙</system:String>
    <system:String x:Key="Icon.SystemMode">🖥️</system:String>
    
    <!-- Calendar Icons -->
    <system:String x:Key="Icon.Calendar">📅</system:String>
    <system:String x:Key="Icon.Event">📆</system:String>
    <system:String x:Key="Icon.Meeting">🤝</system:String>
    <system:String x:Key="Icon.Reminder">⏰</system:String>

    <!-- Contact Icons -->
    <system:String x:Key="Icon.Contact">👤</system:String>
    <system:String x:Key="Icon.Contacts">👥</system:String>
    <system:String x:Key="Icon.Group">👥</system:String>
    <system:String x:Key="Icon.Phone">📞</system:String>
    <system:String x:Key="Icon.Mobile">📱</system:String>
    <system:String x:Key="Icon.Address">🏠</system:String>

    <!-- File Icons -->
    <system:String x:Key="Icon.File">📄</system:String>
    <system:String x:Key="Icon.Image">🖼️</system:String>
    <system:String x:Key="Icon.Document">📄</system:String>
    <system:String x:Key="Icon.PDF">📄</system:String>
    <system:String x:Key="Icon.Spreadsheet">📊</system:String>
    <system:String x:Key="Icon.Presentation">📈</system:String>
    <system:String x:Key="Icon.Archive">📦</system:String>
    <system:String x:Key="Icon.Download">⬇️</system:String>
    <system:String x:Key="Icon.Upload">⬆️</system:String>
    
    <!-- AI Icons -->
    <system:String x:Key="Icon.AI">🤖</system:String>
    <system:String x:Key="Icon.Sparkles">✨</system:String>
    <system:String x:Key="Icon.Magic">🪄</system:String>
    <system:String x:Key="Icon.Brain">🧠</system:String>
    <system:String x:Key="Icon.Lightning">⚡</system:String>

    <!-- Priority Icons -->
    <system:String x:Key="Icon.PriorityHigh">🔴</system:String>
    <system:String x:Key="Icon.PriorityMedium">🟡</system:String>
    <system:String x:Key="Icon.PriorityLow">🟢</system:String>

    <!-- Security Icons -->
    <system:String x:Key="Icon.Lock">🔒</system:String>
    <system:String x:Key="Icon.Unlock">🔓</system:String>
    <system:String x:Key="Icon.Shield">🛡️</system:String>
    <system:String x:Key="Icon.Key">🔑</system:String>

    <!-- Formatting Icons -->
    <system:String x:Key="Icon.Bold">B</system:String>
    <system:String x:Key="Icon.Italic">I</system:String>
    <system:String x:Key="Icon.Underline">U</system:String>
    <system:String x:Key="Icon.Link">🔗</system:String>
    <system:String x:Key="Icon.List">📋</system:String>
    <system:String x:Key="Icon.Quote">💬</system:String>

    <!-- Arrow Icons -->
    <system:String x:Key="Icon.ArrowUp">⬆️</system:String>
    <system:String x:Key="Icon.ArrowDown">⬇️</system:String>
    <system:String x:Key="Icon.ArrowLeft">⬅️</system:String>
    <system:String x:Key="Icon.ArrowRight">➡️</system:String>
    <system:String x:Key="Icon.ChevronUp">⌃</system:String>
    <system:String x:Key="Icon.ChevronDown">⌄</system:String>
    <system:String x:Key="Icon.ChevronLeft">‹</system:String>
    <system:String x:Key="Icon.ChevronRight">›</system:String>
    
    <!-- Modern Icon Styles -->
    <Style x:Key="ModernIconStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI Emoji, Segoe MDL2 Assets"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>
    
    <Style x:Key="LargeIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="FontSize" Value="24"/>
    </Style>
    
    <Style x:Key="SmallIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="FontSize" Value="12"/>
    </Style>
    
    <Style x:Key="PrimaryIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>
    
    <Style x:Key="SuccessIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
    </Style>
    
    <Style x:Key="WarningIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
    </Style>
    
    <Style x:Key="ErrorIconStyle" TargetType="TextBlock" BasedOn="{StaticResource ModernIconStyle}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
    </Style>
    
    <!-- Status Indicator Styles -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Border">
        <Setter Property="Width" Value="8"/>
        <Setter Property="Height" Value="8"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
    </Style>
    
    <Style x:Key="UnreadIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    </Style>
    
    <Style x:Key="OfflineIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
    </Style>
    
    <Style x:Key="SyncingIndicatorStyle" TargetType="Border" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
    </Style>
    
    <!-- Badge Styles -->
    <Style x:Key="BadgeStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="CornerRadius" Value="10"/>
        <Setter Property="Padding" Value="6,2"/>
        <Setter Property="MinWidth" Value="20"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>
    
    <Style x:Key="BadgeTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>
    
    <Style x:Key="SecondaryBadgeStyle" TargetType="Border" BasedOn="{StaticResource BadgeStyle}">
        <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
    </Style>
    
    <Style x:Key="SecondaryBadgeTextStyle" TargetType="TextBlock" BasedOn="{StaticResource BadgeTextStyle}">
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
    </Style>
    
</ResourceDictionary>
