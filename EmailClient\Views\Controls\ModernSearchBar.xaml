<UserControl x:Class="EmailClient.Views.Controls.ModernSearchBar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="300">
    
    <Border Background="{StaticResource SurfaceBrush}"
            BorderBrush="{StaticResource BorderBrush}"
            BorderThickness="1"
            CornerRadius="6"
            Padding="12,8"
            MinWidth="250">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <!-- Search Icon -->
            <TextBlock Grid.Column="0"
                       Text="{StaticResource Icon.Search}"
                       Style="{StaticResource ModernIconStyle}"
                       FontSize="14"
                       Margin="0,0,8,0"
                       Foreground="{StaticResource TextMutedBrush}"
                       VerticalAlignment="Center"/>
            
            <!-- Search TextBox -->
            <TextBox Grid.Column="1" 
                     x:Name="SearchTextBox"
                     Background="Transparent"
                     BorderThickness="0"
                     Padding="0"
                     FontSize="14"
                     VerticalContentAlignment="Center"
                     Text="{Binding SearchQuery, UpdateSourceTrigger=PropertyChanged}"
                     KeyDown="SearchTextBox_KeyDown"
                     GotFocus="SearchTextBox_GotFocus"
                     LostFocus="SearchTextBox_LostFocus">
                <TextBox.Style>
                    <Style TargetType="TextBox">
                        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                        <Style.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <VisualBrush AlignmentX="Left" AlignmentY="Center" Stretch="None">
                                            <VisualBrush.Visual>
                                                <Label Content="Search emails..." 
                                                       Foreground="{StaticResource TextMutedBrush}"
                                                       FontSize="14"
                                                       Padding="0"/>
                                            </VisualBrush.Visual>
                                        </VisualBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="Background" Value="Transparent"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </TextBox.Style>
            </TextBox>
            
            <!-- Clear Button -->
            <Button Grid.Column="2" 
                    x:Name="ClearButton"
                    Style="{StaticResource ModernIconButtonStyle}"
                    Width="20" Height="20"
                    Padding="2"
                    Margin="4,0,0,0"
                    Visibility="Collapsed"
                    Click="ClearButton_Click">
                <TextBlock Text="{StaticResource Icon.Close}"
                           Style="{StaticResource ModernIconStyle}"
                           FontSize="10"
                           Foreground="{StaticResource TextMutedBrush}"/>
            </Button>
        </Grid>
    </Border>
</UserControl>
