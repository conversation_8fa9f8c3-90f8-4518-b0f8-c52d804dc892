import { Link, useLocation } from "wouter";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { 
  Mail, Users, CheckSquare, Calendar, FileText, 
  Lightbulb, Settings, HelpCircle, Zap, Inbox, MessageSquare, Workflow
} from "lucide-react";
import { ModeToggle } from "@/components/ui/mode-toggle";
import { AccountSwitcher } from "@/components/email/account-switcher";

const navigation = [
  { name: "Inbox", href: "/inbox", icon: Mail, count: 24 },
  { name: "Contacts", href: "/contacts", icon: Users },
  { name: "Tasks", href: "/tasks", icon: CheckSquare, count: 12 },
  { name: "Calendar", href: "/calendar", icon: Calendar },
  { name: "Documents", href: "/documents", icon: FileText },
];

export default function Sidebar() {
  const [location] = useLocation();
  const [selectedAccountId, setSelectedAccountId] = useState<number | undefined>();

  return (
    <div className="w-64 bg-white dark:bg-gray-800 shadow-lg flex flex-col">
      {/* Brand Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Mail className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-lg font-semibold text-gray-900 dark:text-white">IntelInbox</h1>
        </div>
      </div>
      
      {/* Account Switcher */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <AccountSwitcher 
          selectedAccountId={selectedAccountId}
          onAccountChange={setSelectedAccountId}
        />
      </div>
      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-2">
        {navigation.map((item) => {
          const isActive = location === item.href || (item.href === "/inbox" && location === "/");
          return (
            <Link key={item.name} href={item.href}>
              <span
                className={cn(
                  "flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors cursor-pointer",
                  isActive
                    ? "text-white bg-primary"
                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                )}
              >
                <item.icon className="w-5 h-5" />
                <span>{item.name}</span>
                {item.count && (
                  <span className={cn(
                    "ml-auto text-xs px-2 py-1 rounded-full",
                    isActive 
                      ? "bg-white text-primary" 
                      : "bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-300"
                  )}>
                    {item.count}
                  </span>
                )}
              </span>
            </Link>
          );
        })}

        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="px-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            AI Assistant
          </p>
          <Link href="/ai-insights">
            <span className="mt-2 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <div className="w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full ai-pulse"></div>
              <span>Smart Insights</span>
            </span>
          </Link>
          <Link href="/ai-automations">
            <span className="mt-1 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <Zap className="w-5 h-5" />
              <span>AI Automations</span>
            </span>
          </Link>
          <Link href="/ai-scheduler">
            <span className="mt-1 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <Calendar className="w-5 h-5" />
              <span>AI Scheduler</span>
            </span>
          </Link>
          <Link href="/email-templates">
            <span className="mt-1 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <MessageSquare className="w-5 h-5" />
              <span>Email Templates</span>
            </span>
          </Link>
          <Link href="/email-workflows">
            <span className="mt-1 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <Workflow className="w-5 h-5" />
              <span>Email Workflows</span>
            </span>
          </Link>
          <Link href="/attachment-demo">
            <span className="mt-1 flex items-center space-x-3 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg cursor-pointer">
              <FileText className="w-5 h-5" />
              <span>Attachment Handling</span>
            </span>
          </Link>
        </div>
      </nav>
      {/* User Profile & Settings */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">JS</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-white">John Smith</p>
              <p className="text-xs text-gray-500 dark:text-gray-400"><EMAIL></p>
            </div>
          </div>
          <Link href="/settings">
            <Settings className="w-5 h-5 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 cursor-pointer" />
          </Link>
          <ModeToggle />
        </div>
      </div>
    </div>
  );
}
