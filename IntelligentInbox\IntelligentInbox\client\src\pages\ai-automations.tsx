import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Zap, Mail, Archive, Trash2, Reply, Forward, BarChart3, AlertTriangle, Calendar, FileText, Settings, Brain, Lightbulb, Filter } from "lucide-react";
import IntentAnalyzer from "@/components/ai/intent-analyzer";
import ConditionBuilder from "@/components/ai/condition-builder";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest } from "@/lib/api";
import type { AiAutomation, InsertAiAutomation } from "@shared/schema";

const automationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().min(1, "Natural language description of what this automation should do"),
  type: z.enum(["smart_action", "conditional_forward", "meeting_scheduler", "smart_reply", "content_processor", "custom"]),
  aiInstructions: z.string().min(10, "Detailed instructions for AI to understand and execute"),
  conditions: z.object({
    triggerPatterns: z.array(z.string()).optional(),
    attachmentTypes: z.array(z.string()).optional(),
    senderRules: z.array(z.string()).optional(),
    contentKeywords: z.array(z.string()).optional(),
    priority: z.enum(["any", "low", "medium", "high"]).optional(),
  }),
  actions: z.object({
    forwardTo: z.string().optional(),
    replyTemplate: z.string().optional(),
    createCalendarEvent: z.boolean().optional(),
    suggestMeetingTime: z.boolean().optional(),
    customAction: z.string().optional(),
    notifyUser: z.boolean().optional(),
  }),
});

type AutomationFormData = z.infer<typeof automationSchema>;

const automationTypes = [
  { 
    value: "smart_action", 
    label: "Smart Action", 
    description: "Let AI decide the best action based on email content", 
    icon: Zap,
    examples: ["Auto-unsubscribe from newsletters I ignore", "Archive promotional emails", "Flag urgent emails from VIPs"]
  },
  { 
    value: "conditional_forward", 
    label: "Smart Forwarding", 
    description: "Forward emails based on content, attachments, or patterns", 
    icon: Forward,
    examples: ["Forward <NAME_EMAIL>", "Send contract emails to legal team", "Forward resumes to HR"]
  },
  { 
    value: "meeting_scheduler", 
    label: "Meeting Scheduler", 
    description: "Automatically suggest times and create calendar events", 
    icon: Calendar,
    examples: ["Reply with available times when someone requests a meeting", "Create placeholder events for mentioned calls", "Suggest meeting rooms for in-person meetings"]
  },
  { 
    value: "smart_reply", 
    label: "Intelligent Auto-Reply", 
    description: "Context-aware automatic responses", 
    icon: Reply,
    examples: ["Acknowledge receipt of important documents", "Send availability for requested calls", "Auto-reply to customer inquiries with relevant info"]
  },
  { 
    value: "content_processor", 
    label: "Content Processor", 
    description: "Extract and process information from emails", 
    icon: FileText,
    examples: ["Extract invoice data and create expense records", "Save contact details to address book", "Create tasks from action items in emails"]
  },
  { 
    value: "custom", 
    label: "Custom Automation", 
    description: "Define your own unique automation logic", 
    icon: Settings,
    examples: ["Combine multiple actions", "Complex conditional logic", "Integration with external services"]
  },
];

export default function AiAutomations() {
  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [conditionGroups, setConditionGroups] = useState<any[]>([]);
  const [detectedIntent, setDetectedIntent] = useState<any>(null);
  const queryClient = useQueryClient();

  const { data: automations, isLoading } = useQuery({
    queryKey: ["/api/ai-automations"],
  });

  const createMutation = useMutation({
    mutationFn: (data: InsertAiAutomation) => apiRequest("POST", "/api/ai-automations", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ai-automations"] });
      setIsCreateOpen(false);
      form.reset();
    },
  });

  const toggleMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
      apiRequest("PATCH", `/api/ai-automations/${id}`, { isActive }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ai-automations"] });
    },
  });

  const form = useForm<AutomationFormData>({
    resolver: zodResolver(automationSchema),
    defaultValues: {
      name: "",
      description: "",
      type: "smart_action",
      aiInstructions: "",
      conditions: {
        triggerPatterns: [],
        attachmentTypes: [],
        senderRules: [],
        contentKeywords: [],
        priority: "any",
      },
      actions: {
        forwardTo: "",
        replyTemplate: "",
        createCalendarEvent: false,
        suggestMeetingTime: false,
        customAction: "",
        notifyUser: true,
      },
    },
  });

  const selectedType = form.watch("type");

  const onSubmit = (data: AutomationFormData) => {
    createMutation.mutate({
      userId: 1, // Mock user ID for development
      name: data.name,
      description: data.description,
      type: data.type,
      aiInstructions: data.aiInstructions,
      conditions: data.conditions,
      actions: data.actions,
      isActive: true,
    });
  };

  const getAutomationIcon = (type: string) => {
    const automationType = automationTypes.find(at => at.value === type);
    return automationType?.icon || Zap;
  };

  const getStatusColor = (isActive: boolean, triggerCount: number) => {
    if (!isActive) return "secondary";
    if (triggerCount > 50) return "destructive";
    if (triggerCount > 10) return "default";
    return "secondary";
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">AI Automations</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Create intelligent automations to manage your email workflow
          </p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              New Automation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create AI Automation</DialogTitle>
              <DialogDescription>
                Build intelligent email automation with advanced intent recognition and smart conditions
              </DialogDescription>
            </DialogHeader>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Setup</TabsTrigger>
                <TabsTrigger value="conditions">Smart Conditions</TabsTrigger>
                <TabsTrigger value="intent">Intent Analysis</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Automation Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Invoice Forwarding Bot" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Automation Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select automation type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {automationTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center space-x-2">
                                <type.icon className="w-4 h-4" />
                                <span>{type.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {automationTypes.find(at => at.value === selectedType)?.description}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Examples for selected type */}
                {selectedType && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2 flex items-center">
                      <Lightbulb className="w-4 h-4 mr-2" />
                      Example Use Cases
                    </h4>
                    <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                      {automationTypes.find(at => at.value === selectedType)?.examples.map((example, index) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2">•</span>
                          <span>{example}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="aiInstructions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-2">
                        <Brain className="w-4 h-4" />
                        <span>AI Instructions</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Tell the AI exactly what you want it to do in plain English. For example: 'When I receive an email with an invoice attachment, forward <NAME_EMAIL> and reply to the sender confirming receipt.'"
                          rows={4}
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Be specific about triggers, conditions, and actions. The AI will interpret and execute these instructions.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Short Description</FormLabel>
                      <FormControl>
                        <Input placeholder="Brief summary for easy identification" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Conditional Action Fields */}
                {(selectedType === "conditional_forward" || selectedType === "meeting_scheduler" || selectedType === "smart_reply") && (
                  <div className="space-y-4 border-t pt-4">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">Additional Settings</h4>
                    
                    {(selectedType === "conditional_forward") && (
                      <FormField
                        control={form.control}
                        name="actions.forwardTo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Forward To Email</FormLabel>
                            <FormControl>
                              <Input placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormDescription>Email address to forward matching emails to</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {selectedType === "meeting_scheduler" && (
                      <div className="space-y-3">
                        <FormField
                          control={form.control}
                          name="actions.createCalendarEvent"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Auto-create calendar events</FormLabel>
                                <FormDescription>
                                  Automatically add placeholder events to calendar
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="actions.suggestMeetingTime"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Suggest available times</FormLabel>
                                <FormDescription>
                                  Reply with your available meeting times
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {(selectedType === "smart_reply") && (
                      <FormField
                        control={form.control}
                        name="actions.replyTemplate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reply Template (Optional)</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Use {sender_name}, {email_subject}, and other variables in your template..."
                                rows={3}
                                {...field} 
                              />
                            </FormControl>
                            <FormDescription>Leave blank to let AI generate contextual replies</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={createMutation.isPending}>
                    {createMutation.isPending ? "Creating..." : "Create AI Automation"}
                  </Button>
                </div>
                  </form>
                </Form>
              </TabsContent>
              
              <TabsContent value="conditions">
                <ConditionBuilder 
                  onConditionsChange={(groups) => setConditionGroups(groups)}
                  initialConditions={conditionGroups}
                />
              </TabsContent>
              
              <TabsContent value="intent">
                <IntentAnalyzer 
                  emailText=""
                  onIntentDetected={(intent) => setDetectedIntent(intent)}
                  autoAnalyze={false}
                />
                
                {detectedIntent && (
                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle className="text-sm">Apply Intent to Automation</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            Intent: {detectedIntent.intent}
                          </Badge>
                          <Badge variant="outline">
                            Confidence: {Math.round(detectedIntent.confidence * 100)}%
                          </Badge>
                        </div>
                        
                        <div className="text-sm space-y-2">
                          <p><strong>Suggested Actions:</strong></p>
                          <ul className="space-y-1">
                            {detectedIntent.suggestedActions.map((action: string, index: number) => (
                              <li key={index} className="flex items-center space-x-2">
                                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span>{action}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Active Automations
                </p>
                <p className="text-2xl font-bold text-blue-600">
                  {automations?.filter(a => a.isActive).length || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Total Triggers
                </p>
                <p className="text-2xl font-bold text-green-600">
                  {automations?.reduce((sum, a) => sum + a.triggerCount, 0) || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  High Activity
                </p>
                <p className="text-2xl font-bold text-orange-600">
                  {automations?.filter(a => a.triggerCount > 50).length || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Automations List */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Your Automations
        </h2>
        
        {automations?.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No automations yet
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create intelligent automations to handle your emails automatically
              </p>
              <div className="space-y-3 mb-6">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <strong>Popular examples:</strong>
                </div>
                <div className="text-left max-w-md mx-auto space-y-2 text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex items-start space-x-2">
                    <Forward className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span>"Forward any email with invoice <NAME_EMAIL>"</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Calendar className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>"When someone requests a meeting, reply with my available times"</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Reply className="w-4 h-4 text-purple-500 mt-0.5 flex-shrink-0" />
                    <span>"Auto-reply to customer inquiries with helpful information"</span>
                  </div>
                </div>
              </div>
              <Button onClick={() => setIsCreateOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Automation
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-3">
            {automations?.map((automation) => {
              const IconComponent = getAutomationIcon(automation.type);
              return (
                <Card key={automation.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <IconComponent className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900 dark:text-white">
                            {automation.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {automation.description}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant={getStatusColor(automation.isActive, automation.triggerCount)}>
                              {automation.isActive ? "Active" : "Inactive"}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              Triggered {automation.triggerCount} times
                            </span>
                            {automation.lastTriggered && (
                              <span className="text-xs text-gray-500">
                                Last: {new Date(automation.lastTriggered).toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={automation.isActive}
                        onCheckedChange={(checked) => 
                          toggleMutation.mutate({ id: automation.id, isActive: checked })
                        }
                      />
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}