using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views.Controls;

public partial class ModernSidebar : UserControl
{
    public static readonly RoutedEvent NavigationRequestedEvent = 
        EventManager.RegisterRoutedEvent("NavigationRequested", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernSidebar));

    public static readonly RoutedEvent AccountSwitchRequestedEvent = 
        EventManager.RegisterRoutedEvent("AccountSwitchRequested", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernSidebar));

    public static readonly RoutedEvent SettingsRequestedEvent = 
        EventManager.RegisterRoutedEvent("SettingsRequested", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernSidebar));

    public event RoutedEventHandler NavigationRequested
    {
        add { AddHandler(NavigationRequestedEvent, value); }
        remove { RemoveHandler(NavigationRequestedEvent, value); }
    }

    public event RoutedEventHandler AccountSwitchRequested
    {
        add { AddHandler(AccountSwitchRequestedEvent, value); }
        remove { RemoveHandler(AccountSwitchRequestedEvent, value); }
    }

    public event RoutedEventHandler SettingsRequested
    {
        add { AddHandler(SettingsRequestedEvent, value); }
        remove { RemoveHandler(SettingsRequestedEvent, value); }
    }

    public ModernSidebar()
    {
        InitializeComponent();
    }

    private void NavigationButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string navigationTarget)
        {
            // Update active state
            UpdateActiveButton(button);
            
            // Raise navigation event
            var args = new NavigationEventArgs(NavigationRequestedEvent, this)
            {
                NavigationTarget = navigationTarget
            };
            RaiseEvent(args);
        }
    }

    private void AccountSwitcherButton_Click(object sender, RoutedEventArgs e)
    {
        var args = new RoutedEventArgs(AccountSwitchRequestedEvent, this);
        RaiseEvent(args);
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        var args = new RoutedEventArgs(SettingsRequestedEvent, this);
        RaiseEvent(args);
    }

    private void UpdateActiveButton(Button activeButton)
    {
        // Reset all navigation buttons to default state
        foreach (var button in new[] { InboxButton, ContactsButton, TasksButton, CalendarButton, DocumentsButton })
        {
            if (button != null)
            {
                button.Background = System.Windows.Media.Brushes.Transparent;
                button.Foreground = (System.Windows.Media.Brush)FindResource("TextSecondaryBrush");
                
                // Update text color for child TextBlocks
                if (button.Content is Grid grid)
                {
                    foreach (var child in grid.Children)
                    {
                        if (child is TextBlock textBlock && textBlock != InboxBadge?.Child)
                        {
                            textBlock.Foreground = (System.Windows.Media.Brush)FindResource("TextSecondaryBrush");
                        }
                    }
                }
            }
        }

        // Set active button state
        if (activeButton != null)
        {
            activeButton.Background = (System.Windows.Media.Brush)FindResource("SidebarItemActiveBrush");
            activeButton.Foreground = System.Windows.Media.Brushes.White;
            
            // Update text color for child TextBlocks
            if (activeButton.Content is Grid grid)
            {
                foreach (var child in grid.Children)
                {
                    if (child is TextBlock textBlock && textBlock != InboxBadge?.Child)
                    {
                        textBlock.Foreground = System.Windows.Media.Brushes.White;
                    }
                }
            }
        }
    }

    public void UpdateUnreadCount(int count)
    {
        if (InboxBadge?.Child is TextBlock badgeText)
        {
            badgeText.Text = count.ToString();
            InboxBadge.Visibility = count > 0 ? Visibility.Visible : Visibility.Collapsed;
        }
    }

    public void UpdateAccountInfo(string accountName, string accountInfo)
    {
        if (AccountNameText != null)
            AccountNameText.Text = accountName;
        if (AccountInfoText != null)
            AccountInfoText.Text = accountInfo;
    }

    public void SetActiveNavigation(string navigationTarget)
    {
        Button? targetButton = navigationTarget switch
        {
            "Inbox" => InboxButton,
            "Contacts" => ContactsButton,
            "Tasks" => TasksButton,
            "Calendar" => CalendarButton,
            "Documents" => DocumentsButton,
            _ => InboxButton
        };

        if (targetButton != null)
        {
            UpdateActiveButton(targetButton);
        }
    }
}

public class NavigationEventArgs : RoutedEventArgs
{
    public string NavigationTarget { get; set; } = string.Empty;

    public NavigationEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
    {
    }
}
