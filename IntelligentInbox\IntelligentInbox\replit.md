# IntelInbox - AI-Powered Intelligent Email Management Platform

## Overview

IntelInbox is a comprehensive email management platform that unifies multiple inboxes with AI-powered automation capabilities. The application provides intelligent email processing, automated actions, smart relationship detection, and contextual insights to help users manage their email workflow more effectively. Features include AI automations for email management, unified inbox support, and adaptive dark/light mode interface.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: Radix UI components with custom Tailwind CSS styling
- **Styling**: Tailwind CSS with CSS custom properties for theming
- **Build Tool**: Vite for development and production builds

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Database**: PostgreSQL with Neon serverless connection
- **ORM**: Drizzle ORM for type-safe database operations
- **Real-time Communication**: WebSocket server for live updates
- **AI Integration**: Local AI service prepared for Ollama integration

### Key Components

#### Database Schema
- **Users**: Authentication and user management
- **Contacts**: Contact information with company details
- **Emails**: Email management with AI summaries and priority scoring
- **Tasks**: Task management with status tracking and priorities
- **Calendar Events**: Calendar integration with scheduling
- **Documents**: Document storage and management
- **AI Insights**: Automated relationship detection and suggestions

#### API Structure
- RESTful endpoints for CRUD operations on all entities
- WebSocket connections for real-time notifications
- AI services for email analysis and relationship detection
- Session-based authentication (development uses mock user ID)

#### AI Features
- **Email Summarization**: Automatic extraction of key points and action items
- **Priority Detection**: AI-powered email priority classification
- **Relationship Insights**: Detection of conflicts, follow-ups, and related items
- **Smart Suggestions**: Automated recommendations for scheduling and tasks

## Data Flow

1. **Email Processing**: Incoming emails are analyzed by AI for summary, priority, and relationships
2. **Real-time Updates**: WebSocket connections push notifications for new emails and task updates
3. **Cross-Entity Relationships**: AI identifies connections between emails, tasks, contacts, and calendar events
4. **Intelligent Insights**: System generates actionable suggestions based on communication patterns

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connection
- **drizzle-orm**: Type-safe database operations
- **@tanstack/react-query**: Server state management
- **@radix-ui/react-***: Accessible UI components
- **wouter**: Lightweight React routing
- **ws**: WebSocket server implementation

### AI and Processing
- **Local AI**: Prepared for Ollama integration with Windows GPU support
- **date-fns**: Date manipulation and formatting
- **connect-pg-simple**: PostgreSQL session storage

### Development Tools
- **Vite**: Build tool with React plugin
- **TypeScript**: Static type checking
- **Tailwind CSS**: Utility-first CSS framework
- **ESBuild**: Production bundling

## Deployment Strategy

### Development Environment
- **Server**: Node.js with tsx for TypeScript execution
- **Client**: Vite dev server with hot module replacement
- **Database**: Neon serverless PostgreSQL
- **WebSocket**: Integrated with HTTP server

### Production Build
- **Client**: Vite production build to `dist/public`
- **Server**: ESBuild bundling to `dist/index.js`
- **Database**: Drizzle migrations for schema management
- **Environment**: NODE_ENV=production with environment variables

### Key Configuration
- **Database URL**: Required environment variable for PostgreSQL connection
- **Session Management**: PostgreSQL-backed sessions
- **Static Assets**: Served from built client directory

### AI Integration Status
The application features a unified AI insights system with user feedback learning:

#### Current Implementation
- **Unified AI Panel**: Single contextual panel that replaces separate context and insights columns
- **Context-Aware**: AI panel adapts content based on user's current view (email, task, calendar, or default)
- **Smart Suggestions**: AI-generated recommendations for tasks, scheduling, follow-ups, and priority changes
- **Relationship Detection**: Automatic identification of related items across emails, tasks, events, contacts, and documents
- **User Feedback System**: Approval/rejection buttons on all suggestions to train the AI system
- **Learning Framework**: Backend API for processing user feedback to improve future suggestions

#### UI Structure
- **Email View**: Email list → Email detail → Unified AI panel (email context + suggestions)
- **Tasks View**: Task list → Task detail → Unified AI panel (task context + suggestions) 
- **Calendar View**: Calendar → Event detail → Unified AI panel (event context + suggestions)
- **Smart Insights Page**: Comprehensive view of all AI suggestions with feedback controls

#### Recent Changes (January 2025)
- **Fully unified AI system implementation completed** - Successfully consolidated all context and insights into single column
- Removed separate context panel components (EmailContextPanel, TaskContextPanel, CalendarContextPanel, AiAssistantPanel)
- Fixed duplicate column issue by removing redundant UnifiedAiPanel from MainLayout component
- Restructured UnifiedAiPanel to eliminate visual section separators and headers
- Added contextual AI insights that change based on user's current activity
- Implemented feedback API endpoint for AI learning system
- Enhanced Smart Insights page with approval/rejection functionality and learning system explanation
- **User confirmed**: Single unified AI column now working correctly across all pages
- **AI Automations enhanced to be fully flexible** - Natural language instruction system for complex automation scenarios
- **One-Click Attachment Handling implemented** - Smart Destination Routing with AI-powered attachment processing
- Added comprehensive AttachmentHandler component with smart routing analysis and processing capabilities
- Created demonstration page for attachment processing workflow with sample emails
- Enhanced backend API with attachment analysis and processing endpoints
- **Email Intent Recognition system implemented** - Advanced NLP analysis for automated email categorization and action suggestions
- **Smart Email Condition Builder added** - Complex automation rule creation with natural language parsing and visual condition builder
- Enhanced automation creation with tabbed interface supporting intent analysis and advanced condition building
- Added backend APIs for intent recognition and condition evaluation with comprehensive entity extraction
- **AI Meeting Scheduler with Travel Time Intelligence completed (January 10, 2025)** - Full implementation of intelligent scheduling system
- Added comprehensive Settings page with working hours, timezone, and default location configuration
- Implemented SchedulerService with travel time calculations between consecutive appointments
- Created MeetingScheduler component with conflict detection and available slot finding
- Added AI email analysis for automatic meeting duration and location suggestions
- Enhanced database schema with travel time fields and user settings (defaultLocation, workingHours)
- **User confirmed**: AI scheduler working as expected with all requested features functional
- **Multi-Account Email Management implemented (January 10, 2025)** - Full support for multiple email accounts simultaneously
- Enhanced database schema with improved inbox management including sync status, unread counts, and provider-specific settings
- Created AccountSwitcher component with visual indicators for sync status, unread counts, and provider icons
- Updated sidebar to display account switcher prominently with real-time status updates
- Added support for Gmail, Outlook, Yahoo, IMAP, and Exchange providers with distinctive visual branding
- Enhanced inbox page to filter emails by selected account with account-specific header display
- Created sample email workflows, templates, and folders for realistic business scenarios
- Added multi-provider inbox filtering and account-aware email management throughout the interface

#### Future Ollama Integration
- **Target Setup**: Windows host with dedicated GPU
- **AI Service**: Replace current mock service with Ollama API calls
- **Models**: User will choose LLM model for local hosting
- **Features**: Full AI-powered email analysis, relationship mapping, and smart suggestions

The application follows a monorepo structure with shared types between client and server, ensuring type safety across the full stack. The unified AI system provides intelligent automation while maintaining user control and learning from their preferences.