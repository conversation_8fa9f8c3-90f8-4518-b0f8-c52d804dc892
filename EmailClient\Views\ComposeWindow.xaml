<Window x:Class="EmailClient.Views.ComposeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:EmailClient.Converters"
        Title="Compose Email" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="{StaticResource BackgroundBrush}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="{StaticResource SurfaceBrush}" 
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,0,1"
                Padding="24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="✉" FontSize="20" Margin="0,0,12,0" VerticalAlignment="Center"/>
                    <TextBlock Text="Compose Email" 
                               Style="{StaticResource HeadingMediumStyle}"
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Style="{StaticResource ModernIconButtonStyle}"
                            ToolTip="Minimize"
                            Click="MinimizeButton_Click"
                            Margin="0,0,8,0">
                        <TextBlock Text="🗕" FontSize="14"/>
                    </Button>
                    <Button Style="{StaticResource ModernIconButtonStyle}"
                            ToolTip="Close"
                            Click="CloseButton_Click">
                        <TextBlock Text="✕" FontSize="14"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Recipients Section -->
        <Border Grid.Row="1" 
                Background="{StaticResource SurfaceBrush}"
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,0,1"
                Padding="24,16">
            <StackPanel>
                <!-- To Field -->
                <Grid Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="To:" 
                               Style="{StaticResource BodyTextStyle}"
                               VerticalAlignment="Center"/>
                    
                    <TextBox Grid.Column="1" 
                             x:Name="ToTextBox"
                             Style="{StaticResource ModernTextBoxStyle}"
                             Text="{Binding ToAddresses, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,12,0"/>
                    
                    <Button Grid.Column="2" 
                            x:Name="CcBccButton"
                            Content="Cc/Bcc" 
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            Padding="8,4"
                            FontSize="12"
                            Click="CcBccButton_Click"/>
                </Grid>
                
                <!-- Cc/Bcc Fields -->
                <StackPanel x:Name="CcBccPanel" 
                            Visibility="{Binding ShowCcBcc, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" 
                                   Text="Cc:" 
                                   Style="{StaticResource BodyTextStyle}"
                                   VerticalAlignment="Center"/>
                        
                        <TextBox Grid.Column="1" 
                                 x:Name="CcTextBox"
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 Text="{Binding CcAddresses, UpdateSourceTrigger=PropertyChanged}"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" 
                                   Text="Bcc:" 
                                   Style="{StaticResource BodyTextStyle}"
                                   VerticalAlignment="Center"/>
                        
                        <TextBox Grid.Column="1" 
                                 x:Name="BccTextBox"
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 Text="{Binding BccAddresses, UpdateSourceTrigger=PropertyChanged}"/>
                    </Grid>
                </StackPanel>
                
                <!-- Subject Field -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="60"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="Subject:" 
                               Style="{StaticResource BodyTextStyle}"
                               VerticalAlignment="Center"/>
                    
                    <TextBox Grid.Column="1" 
                             x:Name="SubjectTextBox"
                             Style="{StaticResource ModernTextBoxStyle}"
                             Text="{Binding Subject, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,12,0"/>
                    
                    <ComboBox Grid.Column="2" 
                              x:Name="PriorityComboBox"
                              SelectedValue="{Binding Priority}"
                              Width="120"
                              Style="{StaticResource {x:Type ComboBox}}">
                        <ComboBoxItem Content="Low Priority" Tag="Low"/>
                        <ComboBoxItem Content="Normal" Tag="Normal" IsSelected="True"/>
                        <ComboBoxItem Content="High Priority" Tag="High"/>
                    </ComboBox>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Formatting Toolbar -->
        <Border Grid.Row="2" 
                Background="{StaticResource HoverBrush}"
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,0,0,1"
                Padding="24,8">
            <StackPanel Orientation="Horizontal">
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Bold"
                        Margin="0,0,4,0">
                    <TextBlock Text="B" FontWeight="Bold" FontSize="14"/>
                </Button>
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Italic"
                        Margin="0,0,4,0">
                    <TextBlock Text="I" FontStyle="Italic" FontSize="14"/>
                </Button>
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Underline"
                        Margin="0,0,12,0">
                    <TextBlock Text="U" TextDecorations="Underline" FontSize="14"/>
                </Button>
                
                <Border Width="1" Height="20" Background="{StaticResource BorderBrush}" Margin="0,0,12,0"/>
                
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Attach File"
                        Click="AttachButton_Click"
                        Margin="0,0,4,0">
                    <TextBlock Text="📎" FontSize="14"/>
                </Button>
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Insert Image"
                        Margin="0,0,4,0">
                    <TextBlock Text="🖼" FontSize="14"/>
                </Button>
                <Button Style="{StaticResource ModernIconButtonStyle}"
                        ToolTip="Insert Link"
                        Margin="0,0,4,0">
                    <TextBlock Text="🔗" FontSize="14"/>
                </Button>
            </StackPanel>
        </Border>

        <!-- Email Body -->
        <Border Grid.Row="3" 
                Background="{StaticResource SurfaceBrush}"
                Padding="24,20">
            <TextBox x:Name="BodyTextBox"
                     Style="{StaticResource ModernTextBoxStyle}"
                     Text="{Binding Body, UpdateSourceTrigger=PropertyChanged}"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"
                     FontSize="14"
                     LineHeight="22"
                     Padding="16">
                <TextBox.Resources>
                    <Style TargetType="TextBox" BasedOn="{StaticResource ModernTextBoxStyle}">
                        <Style.Triggers>
                            <Trigger Property="Text" Value="">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <VisualBrush AlignmentX="Left" AlignmentY="Top" Stretch="None">
                                            <VisualBrush.Visual>
                                                <Label Content="Write your email..." 
                                                       Foreground="{StaticResource TextMutedBrush}"
                                                       FontSize="14"
                                                       Padding="16,8"/>
                                            </VisualBrush.Visual>
                                        </VisualBrush>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </TextBox.Resources>
            </TextBox>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="4" 
                Background="{StaticResource HoverBrush}"
                BorderBrush="{StaticResource BorderBrush}" 
                BorderThickness="0,1,0,0"
                Padding="24,16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button x:Name="SendButton"
                            Style="{StaticResource ModernButtonStyle}"
                            Click="SendButton_Click"
                            Margin="0,0,12,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📤" FontSize="14" Margin="0,0,8,0"/>
                            <TextBlock Text="Send"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Content="Save Draft" 
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            Click="SaveDraftButton_Click"
                            Margin="0,0,12,0"/>
                    
                    <Button Content="Discard" 
                            Style="{StaticResource ModernSecondaryButtonStyle}"
                            Click="DiscardButton_Click"/>
                </StackPanel>
                
                <TextBlock Grid.Column="1" 
                           Text="Press Ctrl+Enter to send" 
                           Style="{StaticResource CaptionTextStyle}"
                           VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
