import { storage } from "../storage";
import { add, format, startOfDay, endOfDay, isWithinInterval, addMinutes, subMinutes, parseISO, isAfter, isBefore } from "date-fns";

export interface TimeSlot {
  start: Date;
  end: Date;
  duration: number; // minutes
  available: boolean;
}

export interface MeetingRequest {
  duration: number; // minutes
  preferredTimes?: Date[];
  requiredAttendees?: string[];
  location?: string;
  buffer?: number; // minutes before/after
}

export interface TravelInfo {
  fromLocation: string;
  toLocation: string;
  duration: number; // minutes
  mode: 'driving' | 'walking' | 'transit' | 'bicycling';
}

export class SchedulerService {
  
  /**
   * Get user's working hours and availability preferences
   */
  async getUserWorkingHours(userId: number) {
    const user = await storage.getUser(userId);
    return user?.workingHours || {
      start: "09:00",
      end: "17:00",
      timezone: "America/New_York",
      workDays: [1, 2, 3, 4, 5] // Mon-Fri
    };
  }

  /**
   * Find available time slots for a meeting
   */
  async findAvailableSlots(
    userId: number, 
    request: MeetingRequest,
    searchDays: number = 14
  ): Promise<TimeSlot[]> {
    const workingHours = await this.getUserWorkingHours(userId);
    const existingEvents = await storage.getCalendarEvents(userId);
    const availableSlots: TimeSlot[] = [];
    
    const startDate = new Date();
    
    for (let dayOffset = 0; dayOffset < searchDays; dayOffset++) {
      const currentDate = add(startDate, { days: dayOffset });
      const dayOfWeek = currentDate.getDay();
      
      // Skip if not a working day
      if (!workingHours.workDays.includes(dayOfWeek)) {
        continue;
      }
      
      // Create working hours boundaries for this day
      const [startHour, startMinute] = workingHours.start.split(':').map(Number);
      const [endHour, endMinute] = workingHours.end.split(':').map(Number);
      
      const dayStart = new Date(currentDate);
      dayStart.setHours(startHour, startMinute, 0, 0);
      
      const dayEnd = new Date(currentDate);
      dayEnd.setHours(endHour, endMinute, 0, 0);
      
      // Get events for this day
      const dayEvents = existingEvents.filter(event => {
        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);
        return isWithinInterval(eventStart, { start: startOfDay(currentDate), end: endOfDay(currentDate) }) ||
               isWithinInterval(eventEnd, { start: startOfDay(currentDate), end: endOfDay(currentDate) });
      });
      
      // Sort events by start time
      dayEvents.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
      
      // Find gaps between events
      let currentTime = dayStart;
      
      for (const event of dayEvents) {
        const eventStart = new Date(event.startTime);
        const eventEnd = new Date(event.endTime);
        
        // Add travel time buffer if event has location
        const adjustedEventStart = event.travelTimeFrom 
          ? subMinutes(eventStart, event.travelTimeFrom)
          : eventStart;
        
        const adjustedEventEnd = event.travelTimeTo
          ? addMinutes(eventEnd, event.travelTimeTo)
          : eventEnd;
        
        // Check if there's a gap before this event
        const gapDuration = adjustedEventStart.getTime() - currentTime.getTime();
        const gapMinutes = Math.floor(gapDuration / (1000 * 60));
        
        if (gapMinutes >= request.duration + (request.buffer || 0) * 2) {
          const slotEnd = addMinutes(currentTime, request.duration);
          if (isBefore(slotEnd, adjustedEventStart)) {
            availableSlots.push({
              start: new Date(currentTime),
              end: slotEnd,
              duration: request.duration,
              available: true
            });
          }
        }
        
        // Move to after this event
        currentTime = adjustedEventEnd;
      }
      
      // Check if there's time after the last event
      const remainingTime = dayEnd.getTime() - currentTime.getTime();
      const remainingMinutes = Math.floor(remainingTime / (1000 * 60));
      
      if (remainingMinutes >= request.duration + (request.buffer || 0)) {
        const slotEnd = addMinutes(currentTime, request.duration);
        if (isBefore(slotEnd, dayEnd)) {
          availableSlots.push({
            start: new Date(currentTime),
            end: slotEnd,
            duration: request.duration,
            available: true
          });
        }
      }
    }
    
    return availableSlots.slice(0, 20); // Return top 20 slots
  }

  /**
   * Calculate travel time between two locations
   */
  async calculateTravelTime(
    fromLocation: string,
    toLocation: string,
    mode: 'driving' | 'walking' | 'transit' | 'bicycling' = 'driving'
  ): Promise<TravelInfo> {
    // In production, this would use Google Maps API or similar
    // For now, we'll simulate realistic travel times
    
    const distance = this.estimateDistance(fromLocation, toLocation);
    let duration: number;
    
    switch (mode) {
      case 'walking':
        duration = Math.max(5, distance * 12); // ~12 min per mile
        break;
      case 'bicycling':
        duration = Math.max(3, distance * 4); // ~4 min per mile
        break;
      case 'transit':
        duration = Math.max(10, distance * 8); // ~8 min per mile + wait time
        break;
      case 'driving':
      default:
        duration = Math.max(5, distance * 2.5); // ~2.5 min per mile in city
        break;
    }
    
    return {
      fromLocation,
      toLocation,
      duration: Math.ceil(duration),
      mode
    };
  }

  /**
   * Estimate distance between locations (simplified)
   */
  private estimateDistance(location1: string, location2: string): number {
    // Simple heuristic - in production would use geocoding
    if (location1.toLowerCase() === location2.toLowerCase()) {
      return 0;
    }
    
    // Check for common patterns
    if (location1.includes('virtual') || location2.includes('virtual') ||
        location1.includes('zoom') || location2.includes('zoom') ||
        location1.includes('online') || location2.includes('online')) {
      return 0;
    }
    
    // If same city/area, assume closer
    const words1 = location1.toLowerCase().split(/[\s,]+/);
    const words2 = location2.toLowerCase().split(/[\s,]+/);
    const commonWords = words1.filter(word => words2.includes(word));
    
    if (commonWords.length > 0) {
      return Math.random() * 3 + 1; // 1-4 miles
    }
    
    // Different areas
    return Math.random() * 15 + 5; // 5-20 miles
  }

  /**
   * Auto-add travel time to calendar events
   */
  async addTravelTimeToEvent(eventId: number, userId: number): Promise<void> {
    const event = await storage.getCalendarEvent(eventId);
    if (!event || !event.location) return;
    
    const user = await storage.getUser(userId);
    const userLocation = user?.defaultLocation;
    
    if (!userLocation) return;
    
    // Get events before and after this one
    const allEvents = await storage.getCalendarEvents(userId);
    const eventStart = new Date(event.startTime);
    const eventEnd = new Date(event.endTime);
    
    // Find previous event
    const previousEvents = allEvents
      .filter(e => e.id !== eventId && new Date(e.endTime) <= eventStart)
      .sort((a, b) => new Date(b.endTime).getTime() - new Date(a.endTime).getTime());
    
    // Find next event
    const nextEvents = allEvents
      .filter(e => e.id !== eventId && new Date(e.startTime) >= eventEnd)
      .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());
    
    let travelTimeFrom = 0;
    let travelTimeTo = 0;
    
    // Calculate travel time from previous location
    if (previousEvents.length > 0) {
      const prevEvent = previousEvents[0];
      const fromLocation = prevEvent.location || userLocation;
      const travelInfo = await this.calculateTravelTime(fromLocation, event.location);
      travelTimeFrom = travelInfo.duration;
    } else {
      // Travel from user's default location
      const travelInfo = await this.calculateTravelTime(userLocation, event.location);
      travelTimeFrom = travelInfo.duration;
    }
    
    // Calculate travel time to next location
    if (nextEvents.length > 0) {
      const nextEvent = nextEvents[0];
      const toLocation = nextEvent.location || userLocation;
      const travelInfo = await this.calculateTravelTime(event.location, toLocation);
      travelTimeTo = travelInfo.duration;
    } else {
      // Travel back to user's default location
      const travelInfo = await this.calculateTravelTime(event.location, userLocation);
      travelTimeTo = travelInfo.duration;
    }
    
    // Update the event with travel times
    await storage.updateCalendarEvent(eventId, {
      travelTimeFrom,
      travelTimeTo
    });
  }

  /**
   * Smart meeting suggestion based on email content
   */
  async suggestMeetingFromEmail(
    userId: number,
    emailContent: string,
    senderEmail: string
  ): Promise<{
    suggestedTimes: TimeSlot[];
    meetingDuration: number;
    location?: string;
    priority: 'low' | 'medium' | 'high';
  }> {
    // Analyze email content for meeting hints
    const content = emailContent.toLowerCase();
    
    let duration = 30; // default 30 minutes
    let priority: 'low' | 'medium' | 'high' = 'medium';
    let location: string | undefined;
    
    // Determine duration from content
    if (content.includes('quick') || content.includes('brief') || content.includes('15 min')) {
      duration = 15;
    } else if (content.includes('hour') || content.includes('detailed') || content.includes('workshop')) {
      duration = 60;
    } else if (content.includes('half hour') || content.includes('30 min')) {
      duration = 30;
    }
    
    // Determine priority
    if (content.includes('urgent') || content.includes('asap') || content.includes('emergency')) {
      priority = 'high';
    } else if (content.includes('whenever') || content.includes('no rush')) {
      priority = 'low';
    }
    
    // Check for location hints
    if (content.includes('office') || content.includes('conference room')) {
      const user = await storage.getUser(userId);
      location = user?.defaultLocation || 'Office';
    } else if (content.includes('zoom') || content.includes('video') || content.includes('virtual')) {
      location = 'Virtual/Zoom';
    }
    
    // Find available slots
    const request: MeetingRequest = {
      duration,
      location,
      buffer: priority === 'high' ? 0 : 15 // less buffer for urgent meetings
    };
    
    const searchDays = priority === 'high' ? 7 : 14;
    const suggestedTimes = await this.findAvailableSlots(userId, request, searchDays);
    
    return {
      suggestedTimes: suggestedTimes.slice(0, 5), // Top 5 suggestions
      meetingDuration: duration,
      location,
      priority
    };
  }

  /**
   * Process all events for a user to add travel times
   */
  async processAllUserEvents(userId: number): Promise<void> {
    const events = await storage.getCalendarEvents(userId);
    
    for (const event of events) {
      if (event.location && (!event.travelTimeFrom || !event.travelTimeTo)) {
        await this.addTravelTimeToEvent(event.id, userId);
      }
    }
  }

  /**
   * Check for scheduling conflicts including travel time
   */
  async checkConflicts(userId: number, proposedEvent: {
    startTime: Date;
    endTime: Date;
    location?: string;
  }): Promise<{
    hasConflict: boolean;
    conflicts: Array<{
      eventId: number;
      title: string;
      reason: string;
    }>;
  }> {
    const events = await storage.getCalendarEvents(userId);
    const conflicts = [];
    
    for (const event of events) {
      const eventStart = new Date(event.startTime);
      const eventEnd = new Date(event.endTime);
      
      // Account for travel time
      const bufferedStart = event.travelTimeFrom 
        ? subMinutes(eventStart, event.travelTimeFrom)
        : eventStart;
      
      const bufferedEnd = event.travelTimeTo
        ? addMinutes(eventEnd, event.travelTimeTo)
        : eventEnd;
      
      // Check for time overlap
      if (
        (proposedEvent.startTime >= bufferedStart && proposedEvent.startTime < bufferedEnd) ||
        (proposedEvent.endTime > bufferedStart && proposedEvent.endTime <= bufferedEnd) ||
        (proposedEvent.startTime <= bufferedStart && proposedEvent.endTime >= bufferedEnd)
      ) {
        conflicts.push({
          eventId: event.id,
          title: event.title,
          reason: event.travelTimeFrom || event.travelTimeTo 
            ? 'Conflicts with travel time'
            : 'Time overlap'
        });
      }
    }
    
    return {
      hasConflict: conflicts.length > 0,
      conflicts
    };
  }
}

export const schedulerService = new SchedulerService();