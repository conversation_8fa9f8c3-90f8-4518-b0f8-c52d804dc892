// AI service interface - will be replaced with Ollama integration
// Currently using mock responses until Ollama is set up

export interface EmailSummary {
  summary: string;
  keyPoints: string[];
  actionItems: string[];
  sentiment: "positive" | "negative" | "neutral";
  priority: "low" | "medium" | "high";
}

export interface RelationshipInsight {
  type: "conflict" | "related" | "follow_up" | "meeting_request";
  description: string;
  confidence: number;
  relatedItems: {
    type: "email" | "task" | "calendar" | "contact";
    id: number;
    title: string;
  }[];
}

export interface AiSuggestion {
  type: "schedule" | "task" | "follow_up" | "priority_change";
  title: string;
  description: string;
  actionUrl?: string;
  priority: "low" | "medium" | "high";
}

export async function summarizeEmail(subject: string, body: string): Promise<EmailSummary> {
  // TODO: Replace with Ollama integration
  // For now, return basic analysis based on email content
  
  const wordCount = body.split(' ').length;
  const hasUrgentWords = /urgent|asap|immediately|deadline|important/i.test(subject + " " + body);
  const hasQuestionMarks = body.includes('?');
  const hasActionWords = /please|can you|need to|should|must|required/i.test(body);
  
  // Basic sentiment analysis
  const positiveWords = /good|great|excellent|thanks|appreciate|love|happy|pleased/i;
  const negativeWords = /bad|terrible|problem|issue|error|failed|wrong|disappointed/i;
  let sentiment: "positive" | "negative" | "neutral" = "neutral";
  
  if (positiveWords.test(body)) sentiment = "positive";
  else if (negativeWords.test(body)) sentiment = "negative";
  
  // Basic priority assessment
  let priority: "low" | "medium" | "high" = "medium";
  if (hasUrgentWords) priority = "high";
  else if (wordCount < 50) priority = "low";
  
  return {
    summary: `${subject} - ${wordCount} words, ${hasQuestionMarks ? 'contains questions' : 'informational'}`,
    keyPoints: hasActionWords ? ["Action items identified", "Response may be required"] : ["Informational content"],
    actionItems: hasActionWords ? ["Review and respond as needed"] : [],
    sentiment,
    priority
  };
}

export async function findRelationships(
  emails: any[],
  tasks: any[],
  calendarEvents: any[],
  contacts: any[]
): Promise<RelationshipInsight[]> {
  // TODO: Replace with Ollama integration
  // For now, return basic relationship analysis based on simple pattern matching
  
  const relationships: RelationshipInsight[] = [];
  
  // Find emails from contacts
  emails.forEach(email => {
    const relatedContact = contacts.find(c => c.email === email.sender);
    if (relatedContact) {
      relationships.push({
        type: "related",
        description: `Email from known contact: ${relatedContact.name}`,
        confidence: 0.9,
        relatedItems: [
          { type: "email", id: email.id, title: email.subject },
          { type: "contact", id: relatedContact.id, title: relatedContact.name }
        ]
      });
    }
  });
  
  // Find tasks mentioned in emails
  emails.forEach(email => {
    tasks.forEach(task => {
      const emailContent = (email.subject + " " + email.body).toLowerCase();
      if (emailContent.includes(task.title.toLowerCase())) {
        relationships.push({
          type: "related",
          description: `Task mentioned in email: ${task.title}`,
          confidence: 0.7,
          relatedItems: [
            { type: "email", id: email.id, title: email.subject },
            { type: "task", id: task.id, title: task.title }
          ]
        });
      }
    });
  });
  
  // Find overdue tasks
  const now = new Date();
  tasks.forEach(task => {
    if (task.dueDate && new Date(task.dueDate) < now && !task.completed) {
      relationships.push({
        type: "conflict",
        description: `Task is overdue: ${task.title}`,
        confidence: 1.0,
        relatedItems: [
          { type: "task", id: task.id, title: task.title }
        ]
      });
    }
  });
  
  return relationships;
}

export async function generateSuggestions(
  userContext: {
    recentEmails: any[];
    upcomingTasks: any[];
    todaysEvents: any[];
  }
): Promise<AiSuggestion[]> {
  // TODO: Replace with Ollama integration
  // For now, return basic suggestions based on simple analysis
  
  const suggestions: AiSuggestion[] = [];
  
  // Check for overdue tasks
  const now = new Date();
  const overdueTasks = userContext.upcomingTasks.filter(task => 
    task.dueDate && new Date(task.dueDate) < now && !task.completed
  );
  
  if (overdueTasks.length > 0) {
    suggestions.push({
      type: "priority_change",
      title: "Address Overdue Tasks",
      description: `You have ${overdueTasks.length} overdue tasks that need attention.`,
      priority: "high"
    });
  }
  
  // Check for unread emails
  const unreadEmails = userContext.recentEmails.filter(email => !email.isRead);
  if (unreadEmails.length > 5) {
    suggestions.push({
      type: "task",
      title: "Process Unread Emails",
      description: `You have ${unreadEmails.length} unread emails. Consider setting aside time to process your inbox.`,
      priority: "medium"
    });
  }
  
  // Check for busy day
  if (userContext.todaysEvents.length > 3) {
    suggestions.push({
      type: "schedule",
      title: "Busy Day Ahead",
      description: `You have ${userContext.todaysEvents.length} events today. Consider preparing in advance and blocking time for focused work.`,
      priority: "medium"
    });
  }
  
  // Check for tasks without due dates
  const tasksWithoutDueDate = userContext.upcomingTasks.filter(task => !task.dueDate && !task.completed);
  if (tasksWithoutDueDate.length > 0) {
    suggestions.push({
      type: "task",
      title: "Set Due Dates",
      description: `${tasksWithoutDueDate.length} tasks don't have due dates. Adding deadlines can help with prioritization.`,
      priority: "low"
    });
  }
  
  return suggestions;
}
