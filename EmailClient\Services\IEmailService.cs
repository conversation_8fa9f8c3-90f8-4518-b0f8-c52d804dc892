using EmailClient.Models;

namespace EmailClient.Services;

public interface IEmailService
{
    Task<IEnumerable<EmailMessage>> GetUnifiedInboxAsync();
    Task<IEnumerable<EmailMessage>> GetMessagesForAccountAsync(int accountId);
    Task<IEnumerable<EmailMessage>> GetMessagesForFolderAsync(int folderId);
    Task<EmailMessage?> GetMessageAsync(int messageId);
    Task SyncAccountAsync(int accountId);
    Task SyncAllAccountsAsync();
    Task<IEnumerable<EmailMessage>> SearchAsync(string query);
    Task MarkAsReadAsync(int messageId);
    Task MarkAsUnreadAsync(int messageId);
    Task DeleteMessageAsync(int messageId);
    Task MoveMessageAsync(int messageId, int targetFolderId);
    Task SendEmailAsync(EmailMessage message);
    Task SaveDraftAsync(EmailMessage message);
}
