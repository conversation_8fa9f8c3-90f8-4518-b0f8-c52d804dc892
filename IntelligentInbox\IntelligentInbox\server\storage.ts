import { 
  users, contacts, emails, tasks, calendarEvents, documents, aiInsights, aiAutomations, inboxes,
  emailTemplates, emailFolders, emailWorkflows,
  type User, type InsertUser, type Contact, type InsertContact,
  type Email, type InsertEmail, type Task, type InsertTask,
  type CalendarEvent, type InsertCalendarEvent, type Document, type InsertDocument,
  type AiInsight, type InsertAiInsight, type AiAutomation, type InsertAiAutomation,
  type Inbox, type InsertInbox, type EmailTemplate, type InsertEmailTemplate,
  type EmailFolder, type InsertEmailFolder, type EmailWorkflow, type InsertEmailWorkflow
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, or, ilike, inArray } from "drizzle-orm";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, user: Partial<InsertUser>): Promise<User>;

  // Contact methods
  getContacts(userId: number): Promise<Contact[]>;
  getContact(id: number): Promise<Contact | undefined>;
  createContact(contact: InsertContact): Promise<Contact>;
  updateContact(id: number, contact: Partial<InsertContact>): Promise<Contact>;
  deleteContact(id: number): Promise<void>;

  // Email methods
  getEmails(userId: number, limit?: number): Promise<Email[]>;
  getEmail(id: number): Promise<Email | undefined>;
  createEmail(email: InsertEmail): Promise<Email>;
  updateEmail(id: number, email: Partial<InsertEmail>): Promise<Email>;
  markEmailAsRead(id: number): Promise<void>;
  markEmailAsStarred(id: number, starred: boolean): Promise<void>;

  // Task methods
  getTasks(userId: number): Promise<Task[]>;
  getTask(id: number): Promise<Task | undefined>;
  createTask(task: InsertTask): Promise<Task>;
  updateTask(id: number, task: Partial<InsertTask>): Promise<Task>;
  deleteTask(id: number): Promise<void>;
  markTaskCompleted(id: number): Promise<void>;

  // Calendar methods
  getCalendarEvents(userId: number): Promise<CalendarEvent[]>;
  getCalendarEvent(id: number): Promise<CalendarEvent | undefined>;
  createCalendarEvent(event: InsertCalendarEvent): Promise<CalendarEvent>;
  updateCalendarEvent(id: number, event: Partial<InsertCalendarEvent>): Promise<CalendarEvent>;
  deleteCalendarEvent(id: number): Promise<void>;

  // Document methods
  getDocuments(userId: number): Promise<Document[]>;
  getDocument(id: number): Promise<Document | undefined>;
  createDocument(document: InsertDocument): Promise<Document>;
  updateDocument(id: number, document: Partial<InsertDocument>): Promise<Document>;
  deleteDocument(id: number): Promise<void>;

  // AI Insights methods
  getAiInsights(userId: number): Promise<AiInsight[]>;
  createAiInsight(insight: InsertAiInsight): Promise<AiInsight>;
  markAiInsightAsRead(id: number): Promise<void>;

  // AI Automations methods
  getAiAutomations(userId: number): Promise<AiAutomation[]>;
  getAiAutomation(id: number): Promise<AiAutomation | undefined>;
  createAiAutomation(automation: InsertAiAutomation): Promise<AiAutomation>;
  updateAiAutomation(id: number, automation: Partial<InsertAiAutomation>): Promise<AiAutomation>;
  deleteAiAutomation(id: number): Promise<void>;

  // Inbox methods
  getInboxes(userId: number): Promise<Inbox[]>;
  getInbox(id: number): Promise<Inbox | undefined>;
  createInbox(inbox: InsertInbox): Promise<Inbox>;
  updateInbox(id: number, inbox: Partial<InsertInbox>): Promise<Inbox>;
  deleteInbox(id: number): Promise<void>;

  // Email Templates methods
  getEmailTemplates(userId: number): Promise<EmailTemplate[]>;
  getEmailTemplate(id: number): Promise<EmailTemplate | undefined>;
  createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate>;
  updateEmailTemplate(id: number, template: Partial<InsertEmailTemplate>): Promise<EmailTemplate>;
  deleteEmailTemplate(id: number): Promise<void>;
  getTemplatesByCategory(userId: number, category: string): Promise<EmailTemplate[]>;
  getTemplatesByIntent(userId: number, intent: string): Promise<EmailTemplate[]>;

  // Email Folders methods
  getEmailFolders(userId: number): Promise<EmailFolder[]>;
  getEmailFolder(id: number): Promise<EmailFolder | undefined>;
  createEmailFolder(folder: InsertEmailFolder): Promise<EmailFolder>;
  updateEmailFolder(id: number, folder: Partial<InsertEmailFolder>): Promise<EmailFolder>;
  deleteEmailFolder(id: number): Promise<void>;
  
  // Email Workflows methods
  getEmailWorkflows(userId: number): Promise<EmailWorkflow[]>;
  getEmailWorkflow(id: number): Promise<EmailWorkflow | undefined>;
  createEmailWorkflow(workflow: InsertEmailWorkflow): Promise<EmailWorkflow>;
  updateEmailWorkflow(id: number, workflow: Partial<InsertEmailWorkflow>): Promise<EmailWorkflow>;
  deleteEmailWorkflow(id: number): Promise<void>;
  getActiveWorkflows(userId: number): Promise<EmailWorkflow[]>;
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async updateUser(id: number, updateData: Partial<InsertUser>): Promise<User> {
    const [user] = await db.update(users).set(updateData).where(eq(users.id, id)).returning();
    return user;
  }

  // Contact methods
  async getContacts(userId: number): Promise<Contact[]> {
    return await db.select().from(contacts).where(eq(contacts.userId, userId)).orderBy(desc(contacts.createdAt));
  }

  async getContact(id: number): Promise<Contact | undefined> {
    const [contact] = await db.select().from(contacts).where(eq(contacts.id, id));
    return contact || undefined;
  }

  async createContact(contact: InsertContact): Promise<Contact> {
    const [newContact] = await db.insert(contacts).values(contact).returning();
    return newContact;
  }

  async updateContact(id: number, contact: Partial<InsertContact>): Promise<Contact> {
    const [updatedContact] = await db.update(contacts).set(contact).where(eq(contacts.id, id)).returning();
    return updatedContact;
  }

  async deleteContact(id: number): Promise<void> {
    await db.delete(contacts).where(eq(contacts.id, id));
  }

  // Email methods
  async getEmails(userId: number, limit = 50): Promise<Email[]> {
    return await db.select().from(emails).where(eq(emails.userId, userId)).orderBy(desc(emails.createdAt)).limit(limit);
  }

  async getEmail(id: number): Promise<Email | undefined> {
    const [email] = await db.select().from(emails).where(eq(emails.id, id));
    return email || undefined;
  }

  async createEmail(email: InsertEmail): Promise<Email> {
    const [newEmail] = await db.insert(emails).values(email).returning();
    return newEmail;
  }

  async updateEmail(id: number, email: Partial<InsertEmail>): Promise<Email> {
    const [updatedEmail] = await db.update(emails).set(email).where(eq(emails.id, id)).returning();
    return updatedEmail;
  }

  async markEmailAsRead(id: number): Promise<void> {
    await db.update(emails).set({ isRead: true }).where(eq(emails.id, id));
  }

  async markEmailAsStarred(id: number, starred: boolean): Promise<void> {
    await db.update(emails).set({ isStarred: starred }).where(eq(emails.id, id));
  }

  // Task methods
  async getTasks(userId: number): Promise<Task[]> {
    return await db.select().from(tasks).where(eq(tasks.userId, userId)).orderBy(desc(tasks.createdAt));
  }

  async getTask(id: number): Promise<Task | undefined> {
    const [task] = await db.select().from(tasks).where(eq(tasks.id, id));
    return task || undefined;
  }

  async createTask(task: InsertTask): Promise<Task> {
    const [newTask] = await db.insert(tasks).values(task).returning();
    return newTask;
  }

  async updateTask(id: number, task: Partial<InsertTask>): Promise<Task> {
    const [updatedTask] = await db.update(tasks).set(task).where(eq(tasks.id, id)).returning();
    return updatedTask;
  }

  async deleteTask(id: number): Promise<void> {
    await db.delete(tasks).where(eq(tasks.id, id));
  }

  async markTaskCompleted(id: number): Promise<void> {
    await db.update(tasks).set({ completed: true, completedAt: new Date() }).where(eq(tasks.id, id));
  }

  // Calendar methods
  async getCalendarEvents(userId: number): Promise<CalendarEvent[]> {
    return await db.select().from(calendarEvents).where(eq(calendarEvents.userId, userId)).orderBy(desc(calendarEvents.startTime));
  }

  async getCalendarEvent(id: number): Promise<CalendarEvent | undefined> {
    const [event] = await db.select().from(calendarEvents).where(eq(calendarEvents.id, id));
    return event || undefined;
  }

  async createCalendarEvent(event: InsertCalendarEvent): Promise<CalendarEvent> {
    const [newEvent] = await db.insert(calendarEvents).values(event).returning();
    return newEvent;
  }

  async updateCalendarEvent(id: number, event: Partial<InsertCalendarEvent>): Promise<CalendarEvent> {
    const [updatedEvent] = await db.update(calendarEvents).set(event).where(eq(calendarEvents.id, id)).returning();
    return updatedEvent;
  }

  async deleteCalendarEvent(id: number): Promise<void> {
    await db.delete(calendarEvents).where(eq(calendarEvents.id, id));
  }

  // Document methods
  async getDocuments(userId: number): Promise<Document[]> {
    return await db.select().from(documents).where(eq(documents.userId, userId)).orderBy(desc(documents.createdAt));
  }

  async getDocument(id: number): Promise<Document | undefined> {
    const [document] = await db.select().from(documents).where(eq(documents.id, id));
    return document || undefined;
  }

  async createDocument(document: InsertDocument): Promise<Document> {
    const [newDocument] = await db.insert(documents).values(document).returning();
    return newDocument;
  }

  async updateDocument(id: number, document: Partial<InsertDocument>): Promise<Document> {
    const [updatedDocument] = await db.update(documents).set(document).where(eq(documents.id, id)).returning();
    return updatedDocument;
  }

  async deleteDocument(id: number): Promise<void> {
    await db.delete(documents).where(eq(documents.id, id));
  }

  // AI Insights methods
  async getAiInsights(userId: number): Promise<AiInsight[]> {
    return await db.select().from(aiInsights).where(eq(aiInsights.userId, userId)).orderBy(desc(aiInsights.createdAt));
  }

  async createAiInsight(insight: InsertAiInsight): Promise<AiInsight> {
    const [newInsight] = await db.insert(aiInsights).values(insight).returning();
    return newInsight;
  }

  async markAiInsightAsRead(id: number): Promise<void> {
    await db.update(aiInsights).set({ isRead: true }).where(eq(aiInsights.id, id));
  }

  // AI Automations methods
  async getAiAutomations(userId: number): Promise<AiAutomation[]> {
    return await db.select().from(aiAutomations).where(eq(aiAutomations.userId, userId)).orderBy(desc(aiAutomations.createdAt));
  }

  async getAiAutomation(id: number): Promise<AiAutomation | undefined> {
    const [automation] = await db.select().from(aiAutomations).where(eq(aiAutomations.id, id));
    return automation || undefined;
  }

  async createAiAutomation(automation: InsertAiAutomation): Promise<AiAutomation> {
    const [newAutomation] = await db.insert(aiAutomations).values(automation).returning();
    return newAutomation;
  }

  async updateAiAutomation(id: number, automation: Partial<InsertAiAutomation>): Promise<AiAutomation> {
    const [updatedAutomation] = await db.update(aiAutomations).set(automation).where(eq(aiAutomations.id, id)).returning();
    return updatedAutomation;
  }

  async deleteAiAutomation(id: number): Promise<void> {
    await db.delete(aiAutomations).where(eq(aiAutomations.id, id));
  }

  // Inbox methods
  async getInboxes(userId: number): Promise<Inbox[]> {
    try {
      return await db.select().from(inboxes).where(eq(inboxes.userId, userId)).orderBy(desc(inboxes.createdAt));
    } catch (error) {
      console.error('Error fetching inboxes:', error);
      return [];
    }
  }

  async getInbox(id: number): Promise<Inbox | undefined> {
    const [inbox] = await db.select().from(inboxes).where(eq(inboxes.id, id));
    return inbox || undefined;
  }

  async createInbox(inbox: InsertInbox): Promise<Inbox> {
    const [newInbox] = await db.insert(inboxes).values(inbox).returning();
    return newInbox;
  }

  async updateInbox(id: number, inbox: Partial<InsertInbox>): Promise<Inbox> {
    const [updatedInbox] = await db.update(inboxes).set(inbox).where(eq(inboxes.id, id)).returning();
    return updatedInbox;
  }

  async deleteInbox(id: number): Promise<void> {
    await db.delete(inboxes).where(eq(inboxes.id, id));
  }

  // Email Templates methods
  async getEmailTemplates(userId: number): Promise<EmailTemplate[]> {
    return db.select().from(emailTemplates).where(eq(emailTemplates.userId, userId));
  }

  async getEmailTemplate(id: number): Promise<EmailTemplate | undefined> {
    const [template] = await db.select().from(emailTemplates).where(eq(emailTemplates.id, id));
    return template;
  }

  async createEmailTemplate(template: InsertEmailTemplate): Promise<EmailTemplate> {
    const [newTemplate] = await db.insert(emailTemplates).values(template).returning();
    return newTemplate;
  }

  async updateEmailTemplate(id: number, template: Partial<InsertEmailTemplate>): Promise<EmailTemplate> {
    const [updatedTemplate] = await db
      .update(emailTemplates)
      .set({ ...template, updatedAt: new Date() })
      .where(eq(emailTemplates.id, id))
      .returning();
    return updatedTemplate;
  }

  async deleteEmailTemplate(id: number): Promise<void> {
    await db.delete(emailTemplates).where(eq(emailTemplates.id, id));
  }

  async getTemplatesByCategory(userId: number, category: string): Promise<EmailTemplate[]> {
    return db
      .select()
      .from(emailTemplates)
      .where(and(eq(emailTemplates.userId, userId), eq(emailTemplates.category, category)));
  }

  async getTemplatesByIntent(userId: number, intent: string): Promise<EmailTemplate[]> {
    return db
      .select()
      .from(emailTemplates)
      .where(and(eq(emailTemplates.userId, userId), eq(emailTemplates.intent, intent)));
  }

  // Email Folders methods
  async getEmailFolders(userId: number): Promise<EmailFolder[]> {
    return db.select().from(emailFolders).where(eq(emailFolders.userId, userId));
  }

  async getEmailFolder(id: number): Promise<EmailFolder | undefined> {
    const [folder] = await db.select().from(emailFolders).where(eq(emailFolders.id, id));
    return folder;
  }

  async createEmailFolder(folder: InsertEmailFolder): Promise<EmailFolder> {
    const [newFolder] = await db.insert(emailFolders).values(folder).returning();
    return newFolder;
  }

  async updateEmailFolder(id: number, folder: Partial<InsertEmailFolder>): Promise<EmailFolder> {
    const [updatedFolder] = await db
      .update(emailFolders)
      .set(folder)
      .where(eq(emailFolders.id, id))
      .returning();
    return updatedFolder;
  }

  async deleteEmailFolder(id: number): Promise<void> {
    await db.delete(emailFolders).where(eq(emailFolders.id, id));
  }

  // Email Workflows methods
  async getEmailWorkflows(userId: number): Promise<EmailWorkflow[]> {
    return db.select().from(emailWorkflows).where(eq(emailWorkflows.userId, userId));
  }

  async getEmailWorkflow(id: number): Promise<EmailWorkflow | undefined> {
    const [workflow] = await db.select().from(emailWorkflows).where(eq(emailWorkflows.id, id));
    return workflow;
  }

  async createEmailWorkflow(workflow: InsertEmailWorkflow): Promise<EmailWorkflow> {
    const [newWorkflow] = await db.insert(emailWorkflows).values(workflow).returning();
    return newWorkflow;
  }

  async updateEmailWorkflow(id: number, workflow: Partial<InsertEmailWorkflow>): Promise<EmailWorkflow> {
    const [updatedWorkflow] = await db
      .update(emailWorkflows)
      .set({ ...workflow, updatedAt: new Date() })
      .where(eq(emailWorkflows.id, id))
      .returning();
    return updatedWorkflow;
  }

  async deleteEmailWorkflow(id: number): Promise<void> {
    await db.delete(emailWorkflows).where(eq(emailWorkflows.id, id));
  }

  async getActiveWorkflows(userId: number): Promise<EmailWorkflow[]> {
    return db
      .select()
      .from(emailWorkflows)
      .where(and(eq(emailWorkflows.userId, userId), eq(emailWorkflows.isActive, true)));
  }
}

export const storage = new DatabaseStorage();
