using EmailClient.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views.Controls;

public partial class ModernEmailList : UserControl, INotifyPropertyChanged
{
    public static readonly DependencyProperty EmailMessagesProperty =
        DependencyProperty.Register("EmailMessages", typeof(ObservableCollection<EmailMessageViewModel>), 
            typeof(ModernEmailList), new PropertyMetadata(null, OnEmailMessagesChanged));

    public static readonly DependencyProperty SelectedMessageProperty =
        DependencyProperty.Register("SelectedMessage", typeof(EmailMessageViewModel), 
            typeof(ModernEmailList), new PropertyMetadata(null));

    public static readonly DependencyProperty IsLoadingProperty =
        DependencyProperty.Register("IsLoading", typeof(bool), 
            typeof(ModernEmailList), new PropertyMetadata(false));

    public static readonly RoutedEvent EmailSelectedEvent = 
        EventManager.RegisterRoutedEvent("EmailSelected", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernEmailList));

    public static readonly RoutedEvent EmailStarredEvent = 
        EventManager.RegisterRoutedEvent("EmailStarred", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernEmailList));

    private ObservableCollection<EmailMessageViewModel> _filteredEmails = new();
    private string _currentFilter = "All";

    public event PropertyChangedEventHandler? PropertyChanged;
    public event RoutedEventHandler EmailSelected
    {
        add { AddHandler(EmailSelectedEvent, value); }
        remove { RemoveHandler(EmailSelectedEvent, value); }
    }

    public event RoutedEventHandler EmailStarred
    {
        add { AddHandler(EmailStarredEvent, value); }
        remove { RemoveHandler(EmailStarredEvent, value); }
    }

    public ObservableCollection<EmailMessageViewModel>? EmailMessages
    {
        get { return (ObservableCollection<EmailMessageViewModel>?)GetValue(EmailMessagesProperty); }
        set { SetValue(EmailMessagesProperty, value); }
    }

    public EmailMessageViewModel? SelectedMessage
    {
        get { return (EmailMessageViewModel?)GetValue(SelectedMessageProperty); }
        set { SetValue(SelectedMessageProperty, value); }
    }

    public bool IsLoading
    {
        get { return (bool)GetValue(IsLoadingProperty); }
        set { SetValue(IsLoadingProperty, value); }
    }

    public ObservableCollection<EmailMessageViewModel> FilteredEmails
    {
        get { return _filteredEmails; }
        set
        {
            _filteredEmails = value;
            OnPropertyChanged(nameof(FilteredEmails));
            OnPropertyChanged(nameof(HasEmails));
        }
    }

    public bool HasEmails => FilteredEmails?.Count > 0;

    public ModernEmailList()
    {
        InitializeComponent();
        DataContext = this;
        UpdateFilterButtons();
    }

    private static void OnEmailMessagesChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernEmailList control)
        {
            control.ApplyFilter();
        }
    }

    private void FilterButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string filter)
        {
            _currentFilter = filter;
            UpdateFilterButtons();
            ApplyFilter();
        }
    }

    private void UpdateFilterButtons()
    {
        // Reset all buttons to secondary style
        AllFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");
        UnreadFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");
        ImportantFilterButton.Style = (Style)FindResource("ModernSecondaryButtonStyle");

        // Set active button to primary style
        Button? activeButton = _currentFilter switch
        {
            "All" => AllFilterButton,
            "Unread" => UnreadFilterButton,
            "Important" => ImportantFilterButton,
            _ => AllFilterButton
        };

        if (activeButton != null)
        {
            activeButton.Style = (Style)FindResource("ModernButtonStyle");
        }
    }

    private void ApplyFilter()
    {
        if (EmailMessages == null)
        {
            FilteredEmails = new ObservableCollection<EmailMessageViewModel>();
            return;
        }

        var filtered = EmailMessages.Where(email => _currentFilter switch
        {
            "Unread" => !email.IsRead,
            "Important" => email.IsFlagged,
            _ => true
        }).ToList();

        FilteredEmails = new ObservableCollection<EmailMessageViewModel>(filtered);
    }

    private void StarButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is EmailMessageViewModel email)
        {
            var args = new EmailActionEventArgs(EmailStarredEvent, this)
            {
                Email = email
            };
            RaiseEvent(args);
        }
    }

    public void SetFilter(string filter)
    {
        _currentFilter = filter;
        UpdateFilterButtons();
        ApplyFilter();
    }

    public void RefreshList()
    {
        ApplyFilter();
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}


