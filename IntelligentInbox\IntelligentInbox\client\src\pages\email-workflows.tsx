import { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Edit, Trash2, Play, Pause, Zap, Mail, Folder, Calendar, CheckSquare, FileText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface EmailWorkflow {
  id: number;
  name: string;
  description?: string;
  trigger: {
    type: "intent" | "sender" | "subject" | "priority" | "schedule";
    conditions: Array<{
      field: string;
      operator: string;
      value: string;
    }>;
  };
  actions: Array<{
    type: "reply" | "forward" | "move_folder" | "set_priority" | "create_task" | "schedule_meeting" | "process_attachments";
    config: Record<string, any>;
  }>;
  isActive: boolean;
  executionCount: number;
  lastExecuted?: string;
  createdAt: string;
}

const triggerTypes = [
  { value: "intent", label: "Email Intent", icon: Zap },
  { value: "sender", label: "Sender Address", icon: Mail },
  { value: "subject", label: "Subject Line", icon: Mail },
  { value: "priority", label: "Priority Level", icon: Zap },
  { value: "schedule", label: "Schedule", icon: Calendar }
];

const actionTypes = [
  { value: "reply", label: "Auto Reply", icon: Mail },
  { value: "forward", label: "Forward Email", icon: Mail },
  { value: "move_folder", label: "Move to Folder", icon: Folder },
  { value: "set_priority", label: "Set Priority", icon: Zap },
  { value: "create_task", label: "Create Task", icon: CheckSquare },
  { value: "schedule_meeting", label: "Schedule Meeting", icon: Calendar },
  { value: "process_attachments", label: "Process Attachments", icon: FileText }
];

const operators = [
  { value: "contains", label: "Contains" },
  { value: "equals", label: "Equals" },
  { value: "startsWith", label: "Starts with" },
  { value: "endsWith", label: "Ends with" }
];

export default function EmailWorkflowsPage() {
  const [selectedTab, setSelectedTab] = useState<string>("active");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState<EmailWorkflow | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    trigger: {
      type: "intent" as const,
      conditions: [{ field: "", operator: "contains", value: "" }]
    },
    actions: [{ type: "reply" as const, config: {} }],
    isActive: true
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: workflows = [], isLoading } = useQuery({
    queryKey: ['/api/email-workflows']
  });

  const { data: folders = [] } = useQuery({
    queryKey: ['/api/email-folders']
  });

  const { data: templates = [] } = useQuery({
    queryKey: ['/api/email-templates']
  });

  const createWorkflowMutation = useMutation({
    mutationFn: (workflowData: any) => apiRequest('/api/email-workflows', {
      method: 'POST',
      body: JSON.stringify(workflowData)
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-workflows'] });
      setIsCreateModalOpen(false);
      resetForm();
      toast({ title: "Workflow created successfully" });
    },
    onError: () => {
      toast({ title: "Failed to create workflow", variant: "destructive" });
    }
  });

  const updateWorkflowMutation = useMutation({
    mutationFn: ({ id, ...data }: any) => apiRequest(`/api/email-workflows/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-workflows'] });
      setEditingWorkflow(null);
      toast({ title: "Workflow updated successfully" });
    },
    onError: () => {
      toast({ title: "Failed to update workflow", variant: "destructive" });
    }
  });

  const deleteWorkflowMutation = useMutation({
    mutationFn: (id: number) => apiRequest(`/api/email-workflows/${id}`, {
      method: 'DELETE'
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-workflows'] });
      toast({ title: "Workflow deleted successfully" });
    },
    onError: () => {
      toast({ title: "Failed to delete workflow", variant: "destructive" });
    }
  });

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      trigger: {
        type: "intent",
        conditions: [{ field: "", operator: "contains", value: "" }]
      },
      actions: [{ type: "reply", config: {} }],
      isActive: true
    });
  };

  const handleSubmit = () => {
    if (editingWorkflow) {
      updateWorkflowMutation.mutate({ id: editingWorkflow.id, ...formData });
    } else {
      createWorkflowMutation.mutate(formData);
    }
  };

  const handleEdit = (workflow: EmailWorkflow) => {
    setEditingWorkflow(workflow);
    setFormData({
      name: workflow.name,
      description: workflow.description || "",
      trigger: workflow.trigger,
      actions: workflow.actions,
      isActive: workflow.isActive
    });
  };

  const handleToggleWorkflow = (workflow: EmailWorkflow) => {
    updateWorkflowMutation.mutate({
      id: workflow.id,
      isActive: !workflow.isActive
    });
  };

  const addCondition = () => {
    setFormData({
      ...formData,
      trigger: {
        ...formData.trigger,
        conditions: [...formData.trigger.conditions, { field: "", operator: "contains", value: "" }]
      }
    });
  };

  const updateCondition = (index: number, field: keyof typeof formData.trigger.conditions[0], value: string) => {
    const newConditions = [...formData.trigger.conditions];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setFormData({
      ...formData,
      trigger: { ...formData.trigger, conditions: newConditions }
    });
  };

  const removeCondition = (index: number) => {
    setFormData({
      ...formData,
      trigger: {
        ...formData.trigger,
        conditions: formData.trigger.conditions.filter((_, i) => i !== index)
      }
    });
  };

  const addAction = () => {
    setFormData({
      ...formData,
      actions: [...formData.actions, { type: "reply", config: {} }]
    });
  };

  const updateAction = (index: number, field: string, value: any) => {
    const newActions = [...formData.actions];
    if (field === 'type') {
      newActions[index] = { type: value, config: {} };
    } else {
      newActions[index] = { ...newActions[index], config: { ...newActions[index].config, [field]: value } };
    }
    setFormData({ ...formData, actions: newActions });
  };

  const removeAction = (index: number) => {
    setFormData({
      ...formData,
      actions: formData.actions.filter((_, i) => i !== index)
    });
  };

  const filteredWorkflows = selectedTab === "active" 
    ? workflows.filter((w: EmailWorkflow) => w.isActive)
    : selectedTab === "inactive"
    ? workflows.filter((w: EmailWorkflow) => !w.isActive)
    : workflows;

  const getActionIcon = (actionType: string) => {
    const action = actionTypes.find(a => a.value === actionType);
    return action ? action.icon : Zap;
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2].map(i => (
              <div key={i} className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Email Workflows</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Automate your email management with intelligent workflow rules
          </p>
        </div>
        <Dialog open={isCreateModalOpen || !!editingWorkflow} onOpenChange={(open) => {
          if (!open) {
            setIsCreateModalOpen(false);
            setEditingWorkflow(null);
            resetForm();
          }
        }}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsCreateModalOpen(true)} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Workflow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingWorkflow ? "Edit Workflow" : "Create New Workflow"}</DialogTitle>
              <DialogDescription>
                Set up automated actions based on email triggers and conditions
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Workflow Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Meeting Request Auto-Reply"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <Label>Active</Label>
                </div>
              </div>
              
              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Automatically reply to meeting requests with available time slots"
                  rows={2}
                />
              </div>

              {/* Trigger Section */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-semibold">Trigger</h3>
                </div>
                
                <div>
                  <Label>Trigger Type</Label>
                  <Select 
                    value={formData.trigger.type} 
                    onValueChange={(value: any) => setFormData({ 
                      ...formData, 
                      trigger: { ...formData.trigger, type: value } 
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {triggerTypes.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Conditions</Label>
                  {formData.trigger.conditions.map((condition, index) => (
                    <div key={index} className="flex gap-2 items-end">
                      <div className="flex-1">
                        <Input
                          placeholder="Field"
                          value={condition.field}
                          onChange={(e) => updateCondition(index, 'field', e.target.value)}
                        />
                      </div>
                      <div className="w-32">
                        <Select
                          value={condition.operator}
                          onValueChange={(value) => updateCondition(index, 'operator', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {operators.map(op => (
                              <SelectItem key={op.value} value={op.value}>
                                {op.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex-1">
                        <Input
                          placeholder="Value"
                          value={condition.value}
                          onChange={(e) => updateCondition(index, 'value', e.target.value)}
                        />
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeCondition(index)}
                        disabled={formData.trigger.conditions.length === 1}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                  <Button variant="outline" size="sm" onClick={addCondition}>
                    Add Condition
                  </Button>
                </div>
              </div>

              {/* Actions Section */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Play className="w-5 h-5 text-green-600" />
                  <h3 className="text-lg font-semibold">Actions</h3>
                </div>
                
                {formData.actions.map((action, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <Label>Action Type</Label>
                        <Select
                          value={action.type}
                          onValueChange={(value) => updateAction(index, 'type', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {actionTypes.map(type => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeAction(index)}
                        disabled={formData.actions.length === 1}
                        className="ml-2"
                      >
                        Remove
                      </Button>
                    </div>

                    {/* Action-specific configuration */}
                    {action.type === 'reply' && (
                      <div>
                        <Label>Template</Label>
                        <Select
                          value={action.config.templateId || ""}
                          onValueChange={(value) => updateAction(index, 'templateId', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select template" />
                          </SelectTrigger>
                          <SelectContent>
                            {templates.map((template: any) => (
                              <SelectItem key={template.id} value={template.id.toString()}>
                                {template.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {action.type === 'move_folder' && (
                      <div>
                        <Label>Folder</Label>
                        <Select
                          value={action.config.folderId || ""}
                          onValueChange={(value) => updateAction(index, 'folderId', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select folder" />
                          </SelectTrigger>
                          <SelectContent>
                            {folders.map((folder: any) => (
                              <SelectItem key={folder.id} value={folder.id.toString()}>
                                {folder.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {action.type === 'set_priority' && (
                      <div>
                        <Label>Priority Level</Label>
                        <Select
                          value={action.config.priority || ""}
                          onValueChange={(value) => updateAction(index, 'priority', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {action.type === 'forward' && (
                      <div>
                        <Label>Forward To</Label>
                        <Input
                          placeholder="<EMAIL>"
                          value={action.config.forwardTo || ""}
                          onChange={(e) => updateAction(index, 'forwardTo', e.target.value)}
                        />
                      </div>
                    )}

                    {action.type === 'create_task' && (
                      <div className="space-y-2">
                        <div>
                          <Label>Task Title</Label>
                          <Input
                            placeholder="Follow up on {{subject}}"
                            value={action.config.taskTitle || ""}
                            onChange={(e) => updateAction(index, 'taskTitle', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label>Task Priority</Label>
                          <Select
                            value={action.config.taskPriority || ""}
                            onValueChange={(value) => updateAction(index, 'taskPriority', value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="low">Low</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    )}

                    {action.type === 'process_attachments' && (
                      <div className="space-y-3">
                        <div>
                          <Label>Processing Mode</Label>
                          <Select
                            value={action.config.processingMode || "smart_routing"}
                            onValueChange={(value) => updateAction(index, 'processingMode', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="smart_routing">Smart Destination Routing</SelectItem>
                              <SelectItem value="extract_data">Extract Key Data</SelectItem>
                              <SelectItem value="categorize">Categorize & Archive</SelectItem>
                              <SelectItem value="create_tasks">Create Tasks from Content</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {action.config.processingMode === 'smart_routing' && (
                          <div className="space-y-2">
                            <div>
                              <Label>Default Destination</Label>
                              <Select
                                value={action.config.defaultDestination || ""}
                                onValueChange={(value) => updateAction(index, 'defaultDestination', value)}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select destination" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="accounting">Accounting</SelectItem>
                                  <SelectItem value="hr">Human Resources</SelectItem>
                                  <SelectItem value="legal">Legal</SelectItem>
                                  <SelectItem value="projects">Project Management</SelectItem>
                                  <SelectItem value="archive">General Archive</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label>Confidence Threshold</Label>
                              <Input
                                type="number"
                                min="0"
                                max="1"
                                step="0.1"
                                placeholder="0.7"
                                value={action.config.confidenceThreshold || "0.7"}
                                onChange={(e) => updateAction(index, 'confidenceThreshold', e.target.value)}
                              />
                            </div>
                          </div>
                        )}

                        {action.config.processingMode === 'extract_data' && (
                          <div className="space-y-2">
                            <div>
                              <Label>Data Types to Extract</Label>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {['dates', 'amounts', 'names', 'addresses', 'emails'].map(dataType => (
                                  <div key={dataType} className="flex items-center space-x-2">
                                    <input
                                      type="checkbox"
                                      id={`${index}-${dataType}`}
                                      checked={action.config.extractTypes?.includes(dataType) || false}
                                      onChange={(e) => {
                                        const current = action.config.extractTypes || [];
                                        const updated = e.target.checked 
                                          ? [...current, dataType]
                                          : current.filter(t => t !== dataType);
                                        updateAction(index, 'extractTypes', updated);
                                      }}
                                    />
                                    <label htmlFor={`${index}-${dataType}`} className="text-sm capitalize">
                                      {dataType}
                                    </label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        )}

                        {action.config.processingMode === 'create_tasks' && (
                          <div className="space-y-2">
                            <div>
                              <Label>Task Template</Label>
                              <Input
                                placeholder="Process {{filename}} - Due: {{extracted_date}}"
                                value={action.config.taskTemplate || ""}
                                onChange={(e) => updateAction(index, 'taskTemplate', e.target.value)}
                              />
                            </div>
                            <div>
                              <Label>Auto-assign To</Label>
                              <Input
                                placeholder="department or email"
                                value={action.config.assignTo || ""}
                                onChange={(e) => updateAction(index, 'assignTo', e.target.value)}
                              />
                            </div>
                          </div>
                        )}

                        <div>
                          <Label>Notify on Completion</Label>
                          <div className="flex items-center space-x-2 mt-1">
                            <input
                              type="checkbox"
                              id={`${index}-notify`}
                              checked={action.config.notifyCompletion || false}
                              onChange={(e) => updateAction(index, 'notifyCompletion', e.target.checked)}
                            />
                            <label htmlFor={`${index}-notify`} className="text-sm">
                              Send notification when processing completes
                            </label>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
                
                <Button variant="outline" size="sm" onClick={addAction}>
                  Add Action
                </Button>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsCreateModalOpen(false);
                    setEditingWorkflow(null);
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmit}
                  disabled={!formData.name || formData.trigger.conditions.some(c => !c.field || !c.value)}
                >
                  {editingWorkflow ? "Update" : "Create"} Workflow
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList>
          <TabsTrigger value="all">All Workflows</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="mt-6">
          {filteredWorkflows.length === 0 ? (
            <div className="text-center py-12">
              <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No workflows found
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create your first email workflow to automate your email management
              </p>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Workflow
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredWorkflows.map((workflow: EmailWorkflow) => (
                <Card key={workflow.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Zap className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                          {workflow.name}
                        </CardTitle>
                        <CardDescription>{workflow.description}</CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleWorkflow(workflow)}
                        >
                          {workflow.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(workflow)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteWorkflowMutation.mutate(workflow.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">Trigger:</p>
                        <Badge variant="outline" className="mb-2">
                          {triggerTypes.find(t => t.value === workflow.trigger.type)?.label}
                        </Badge>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          {workflow.trigger.conditions.map((condition, index) => (
                            <div key={index}>
                              {condition.field} {condition.operator} "{condition.value}"
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">Actions:</p>
                        <div className="flex flex-wrap gap-1">
                          {workflow.actions.map((action, index) => {
                            const ActionIcon = getActionIcon(action.type);
                            return (
                              <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                <ActionIcon className="w-3 h-3" />
                                {actionTypes.find(a => a.value === action.type)?.label}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>

                      <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 pt-2 border-t">
                        <span>Executed {workflow.executionCount} times</span>
                        <span className={workflow.isActive ? "text-green-600" : "text-red-600"}>
                          {workflow.isActive ? "Active" : "Inactive"}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}