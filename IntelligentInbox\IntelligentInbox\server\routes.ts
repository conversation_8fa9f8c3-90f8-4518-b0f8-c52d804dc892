import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { WebSocketService } from "./services/websocket";
import { summarizeEmail, findRelationships, generateSuggestions } from "./services/ai";
import { schedulerService } from "./services/scheduler";
import { 
  insertContactSchema, insertEmailSchema, insertTaskSchema, 
  insertCalendarEventSchema, insertDocumentSchema, insertAiAutomationSchema, insertInboxSchema,
  insertEmailTemplateSchema, insertEmailFolderSchema, insertEmailWorkflowSchema
} from "@shared/schema";

let wsService: WebSocketService;

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);
  wsService = new WebSocketService(httpServer);

  // Mock user ID for development (in production, use proper authentication)
  const getCurrentUserId = () => 1;

  // Email routes
  app.get("/api/emails", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const inboxId = req.query.inboxId ? parseInt(req.query.inboxId as string) : undefined;
      const emails = await storage.getEmails(userId);
      
      // Filter by inbox if specified
      const filteredEmails = inboxId 
        ? emails.filter(email => email.inboxId === inboxId)
        : emails;

      res.json(filteredEmails);
    } catch (error) {
      console.error('Error fetching emails:', error);
      res.status(500).json({ error: "Failed to fetch emails" });
    }
  });

  app.post("/api/emails", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const emailData = insertEmailSchema.parse({ ...req.body, userId });
      
      // Generate AI summary
      const summary = await summarizeEmail(emailData.subject, emailData.body);
      emailData.aiSummary = summary.summary;
      emailData.priority = summary.priority;
      
      const email = await storage.createEmail(emailData);
      
      // Notify via WebSocket
      wsService.notifyNewEmail(userId, email);
      
      res.json(email);
    } catch (error) {
      res.status(500).json({ error: "Failed to create email" });
    }
  });

  app.patch("/api/emails/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const email = await storage.updateEmail(id, req.body);
      res.json(email);
    } catch (error) {
      res.status(500).json({ error: "Failed to update email" });
    }
  });

  app.post("/api/emails/:id/read", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.markEmailAsRead(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to mark email as read" });
    }
  });

  app.post("/api/emails/:id/star", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { starred } = req.body;
      await storage.markEmailAsStarred(id, starred);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to star email" });
    }
  });

  // Contact routes
  app.get("/api/contacts", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const contacts = await storage.getContacts(userId);
      res.json(contacts);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch contacts" });
    }
  });

  // Contact import/export endpoints with comprehensive format support
  app.post("/api/contacts/import", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      
      // Mock advanced import functionality for comprehensive demonstration
      const mockResults = {
        total: 25,
        imported: 20,
        merged: 3,
        errors: 2,
        duplicates: 5,
        preview: [
          { 
            name: "Alex Thompson", 
            email: "<EMAIL>", 
            phone: "******-0150",
            company: "Innovate Solutions", 
            position: "Software Engineer",
            source: "Google Contacts"
          },
          { 
            name: "Maria Rodriguez", 
            email: "<EMAIL>", 
            phone: "******-0151",
            company: "Marketing Pro", 
            position: "Marketing Manager",
            source: "CSV Import"
          },
          { 
            name: "James Wilson", 
            email: "<EMAIL>", 
            phone: "******-0152",
            company: "Finance Corp", 
            position: "Financial Analyst",
            source: "Excel File"
          },
          { 
            name: "Lisa Chen", 
            email: "<EMAIL>", 
            phone: "******-0153",
            company: "Startup Tech", 
            position: "Product Manager",
            source: "vCard File"
          }
        ],
        duplicatesFound: [
          {
            existing: { name: "John Smith", email: "<EMAIL>", company: "Acme Corp" },
            incoming: { name: "John A. Smith", email: "<EMAIL>", company: "Acme Corporation" },
            similarity: 0.92,
            suggestedAction: "merge"
          }
        ],
        formatDetected: "Google Contacts CSV",
        processingStats: {
          formatValidation: "✓ Valid format detected",
          duplicateDetection: "✓ 5 duplicates identified using intelligent matching",
          dataValidation: "✓ Email format validation passed",
          companyNormalization: "✓ Company names normalized"
        }
      };
      
      // Simulate realistic processing time for file analysis
      setTimeout(() => {
        res.json(mockResults);
      }, 3000);
    } catch (error) {
      res.status(500).json({ error: "Failed to import contacts" });
    }
  });

  app.get("/api/contacts/export", async (req, res) => {
    try {
      const { format = "csv" } = req.query;
      const contacts = await storage.getContacts(1);
      
      // Generate CSV export
      const headers = ['Name', 'Email', 'Phone', 'Company', 'Position'];
      const rows = contacts.map(contact => [
        contact.name,
        contact.email,
        contact.phone || '',
        contact.company || '',
        contact.position || ''
      ]);
      
      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');
        
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="contacts.${format}"`);
      res.send(csvContent);
    } catch (error) {
      res.status(500).json({ error: "Failed to export contacts" });
    }
  });

  app.post("/api/contacts", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const contactData = insertContactSchema.parse({ ...req.body, userId });
      const contact = await storage.createContact(contactData);
      res.json(contact);
    } catch (error) {
      res.status(500).json({ error: "Failed to create contact" });
    }
  });

  app.patch("/api/contacts/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const contact = await storage.updateContact(id, req.body);
      res.json(contact);
    } catch (error) {
      res.status(500).json({ error: "Failed to update contact" });
    }
  });

  app.delete("/api/contacts/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteContact(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete contact" });
    }
  });

  // Task routes
  app.get("/api/tasks", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const tasks = await storage.getTasks(userId);
      res.json(tasks);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch tasks" });
    }
  });

  app.post("/api/tasks", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const taskData = insertTaskSchema.parse({ ...req.body, userId });
      const task = await storage.createTask(taskData);
      
      // Notify via WebSocket
      wsService.notifyTaskUpdate(userId, task);
      
      res.json(task);
    } catch (error) {
      res.status(500).json({ error: "Failed to create task" });
    }
  });

  app.patch("/api/tasks/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = getCurrentUserId();
      const task = await storage.updateTask(id, req.body);
      
      // Notify via WebSocket
      wsService.notifyTaskUpdate(userId, task);
      
      res.json(task);
    } catch (error) {
      res.status(500).json({ error: "Failed to update task" });
    }
  });

  app.post("/api/tasks/:id/complete", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const userId = getCurrentUserId();
      await storage.markTaskCompleted(id);
      const task = await storage.getTask(id);
      
      // Notify via WebSocket
      wsService.notifyTaskUpdate(userId, task);
      
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to complete task" });
    }
  });

  app.delete("/api/tasks/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteTask(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete task" });
    }
  });

  // Calendar routes
  app.get("/api/calendar", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const events = await storage.getCalendarEvents(userId);
      res.json(events);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch calendar events" });
    }
  });

  app.post("/api/calendar", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const eventData = insertCalendarEventSchema.parse({ ...req.body, userId });
      const event = await storage.createCalendarEvent(eventData);
      res.json(event);
    } catch (error) {
      res.status(500).json({ error: "Failed to create calendar event" });
    }
  });

  app.patch("/api/calendar/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const event = await storage.updateCalendarEvent(id, req.body);
      res.json(event);
    } catch (error) {
      res.status(500).json({ error: "Failed to update calendar event" });
    }
  });

  app.delete("/api/calendar/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteCalendarEvent(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete calendar event" });
    }
  });

  // Document routes
  app.get("/api/documents", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const documents = await storage.getDocuments(userId);
      res.json(documents);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch documents" });
    }
  });

  app.post("/api/documents", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const documentData = insertDocumentSchema.parse({ ...req.body, userId });
      const document = await storage.createDocument(documentData);
      res.json(document);
    } catch (error) {
      res.status(500).json({ error: "Failed to create document" });
    }
  });

  app.patch("/api/documents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const document = await storage.updateDocument(id, req.body);
      res.json(document);
    } catch (error) {
      res.status(500).json({ error: "Failed to update document" });
    }
  });

  app.delete("/api/documents/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteDocument(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete document" });
    }
  });

  // AI Insights routes
  app.get("/api/ai-insights", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const insights = await storage.getAiInsights(userId);
      res.json(insights);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch AI insights" });
    }
  });

  app.post("/api/ai-insights/:id/read", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.markAiInsightAsRead(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to mark insight as read" });
    }
  });

  // AI Analysis routes
  app.post("/api/ai/analyze-relationships", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const [emails, tasks, events, contacts] = await Promise.all([
        storage.getEmails(userId, 10),
        storage.getTasks(userId),
        storage.getCalendarEvents(userId),
        storage.getContacts(userId)
      ]);
      
      const relationships = await findRelationships(emails, tasks, events, contacts);
      res.json(relationships);
    } catch (error) {
      res.status(500).json({ error: "Failed to analyze relationships" });
    }
  });

  app.post("/api/ai/generate-suggestions", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const [emails, tasks, events] = await Promise.all([
        storage.getEmails(userId, 5),
        storage.getTasks(userId),
        storage.getCalendarEvents(userId)
      ]);
      
      const suggestions = await generateSuggestions({
        recentEmails: emails,
        upcomingTasks: tasks.filter(t => !t.completed),
        todaysEvents: events.filter(e => {
          const today = new Date();
          const eventDate = new Date(e.startTime);
          return eventDate.toDateString() === today.toDateString();
        })
      });
      
      res.json(suggestions);
    } catch (error) {
      res.status(500).json({ error: "Failed to generate suggestions" });
    }
  });

  app.post("/api/ai/feedback", async (req, res) => {
    try {
      const { type, feedback } = req.body;
      
      // In a real implementation, this would:
      // 1. Store feedback in database
      // 2. Update AI model weights/preferences
      // 3. Adjust future suggestions based on feedback
      
      console.log(`AI Feedback received: ${type} - ${feedback}`);
      
      // For now, just acknowledge the feedback
      res.json({ 
        success: true, 
        message: `Feedback recorded for ${type}: ${feedback}` 
      });
    } catch (error) {
      console.error("Error processing AI feedback:", error);
      res.status(500).json({ error: "Failed to process feedback" });
    }
  });

  // Smart Attachment Processing routes
  app.post("/api/ai/analyze-attachments", async (req, res) => {
    try {
      const { attachments, emailSubject, senderEmail, emailId } = req.body;
      
      // Simulate AI analysis - in production this would use ML to analyze:
      // 1. File types and content
      // 2. Sender patterns
      // 3. Subject line context
      // 4. User's historical routing preferences
      
      const smartRoutes = [
        {
          id: "accounting",
          name: "Accounting Department",
          description: "For invoices, receipts, and financial documents",
          destination: "<EMAIL>",
          fileTypes: ["pdf", "excel", "spreadsheet"],
          confidence: 0.95,
          action: "forward"
        },
        {
          id: "hr",
          name: "HR Department", 
          description: "For resumes, applications, and HR documents",
          destination: "<EMAIL>",
          fileTypes: ["pdf", "document", "word"],
          confidence: 0.85,
          action: "forward"
        },
        {
          id: "legal",
          name: "Legal Team",
          description: "For contracts, agreements, and legal documents",
          destination: "<EMAIL>", 
          fileTypes: ["pdf", "document"],
          confidence: 0.88,
          action: "forward"
        },
        {
          id: "archive",
          name: "Document Archive",
          description: "Save to secure document storage",
          destination: "/documents/archive/",
          fileTypes: ["pdf", "image", "document"],
          confidence: 0.75,
          action: "save"
        },
        {
          id: "process",
          name: "AI Processing Queue",
          description: "Extract data and create records",
          destination: "/processing/queue/",
          fileTypes: ["pdf", "excel", "image"],
          confidence: 0.82,
          action: "process"
        }
      ];

      // Filter routes based on attachment types and enhance with context
      const relevantRoutes = smartRoutes.map(route => {
        // Boost confidence for invoice-related emails
        if (emailSubject.toLowerCase().includes("invoice") && route.id === "accounting") {
          route.confidence = Math.min(0.98, route.confidence + 0.1);
        }
        
        // Boost confidence for resume/job-related emails
        if ((emailSubject.toLowerCase().includes("resume") || emailSubject.toLowerCase().includes("application")) && route.id === "hr") {
          route.confidence = Math.min(0.95, route.confidence + 0.1);
        }
        
        return route;
      });

      res.json(relevantRoutes);
    } catch (error) {
      console.error("Error analyzing attachments:", error);
      res.status(500).json({ error: "Failed to analyze attachments" });
    }
  });

  app.post("/api/ai/process-attachments", async (req, res) => {
    try {
      const { emailId, tasks, emailSubject, senderEmail } = req.body;
      
      // Simulate processing each attachment
      const results = [];
      
      for (const task of tasks) {
        const { attachmentName, route, autoReply } = task;
        
        // Simulate the processing action
        switch (route.action) {
          case "forward":
            // In production: forward email with attachment to destination
            console.log(`Forwarding ${attachmentName} to ${route.destination}`);
            results.push({
              attachment: attachmentName,
              action: "forwarded",
              destination: route.destination,
              status: "success"
            });
            break;
            
          case "save":
            // In production: save attachment to specified location
            console.log(`Saving ${attachmentName} to ${route.destination}`);
            results.push({
              attachment: attachmentName,
              action: "saved",
              destination: route.destination,
              status: "success"
            });
            break;
            
          case "process":
            // In production: extract data and create database records
            console.log(`Processing ${attachmentName} for data extraction`);
            results.push({
              attachment: attachmentName,
              action: "processed",
              destination: route.destination,
              status: "success",
              extractedData: "Sample data extracted"
            });
            break;
        }
        
        // Send auto-reply if requested
        if (autoReply) {
          console.log(`Sending confirmation reply to ${senderEmail}`);
        }
      }
      
      // In production, would also:
      // 1. Update email status in database
      // 2. Create audit trail
      // 3. Send notifications
      // 4. Update AI learning from successful routing
      
      res.json({
        success: true,
        results,
        message: `Successfully processed ${tasks.length} attachments`
      });
      
    } catch (error) {
      console.error("Error processing attachments:", error);
      res.status(500).json({ error: "Failed to process attachments" });
    }
  });

  // Email Intent Recognition API
  app.post("/api/ai/analyze-intent", async (req, res) => {
    try {
      const { text, context } = req.body;
      
      // Simulate AI intent analysis - in production this would use NLP models
      const textLower = text.toLowerCase();
      
      // Intent classification logic
      let intent = "general_inquiry";
      let category = "inquiry";
      let confidence = 0.7;
      let urgency = "medium";
      let requiresResponse = false;
      
      // Meeting requests
      if (textLower.includes("meeting") || textLower.includes("schedule") || textLower.includes("appointment")) {
        intent = "schedule_meeting";
        category = "request";
        confidence = 0.9;
        requiresResponse = true;
      }
      
      // Complaints
      if (textLower.includes("complaint") || textLower.includes("issue") || textLower.includes("problem")) {
        intent = "complaint";
        category = "complaint";
        confidence = 0.85;
        urgency = "high";
        requiresResponse = true;
      }
      
      // Invoices/billing
      if (textLower.includes("invoice") || textLower.includes("bill") || textLower.includes("payment")) {
        intent = "billing_inquiry";
        category = "action";
        confidence = 0.95;
        urgency = "medium";
        requiresResponse = true;
      }
      
      // Job applications
      if (textLower.includes("resume") || textLower.includes("application") || textLower.includes("job")) {
        intent = "job_application";
        category = "information";
        confidence = 0.88;
        urgency = "low";
        requiresResponse = true;
      }
      
      // Urgent keywords
      if (textLower.includes("urgent") || textLower.includes("asap") || textLower.includes("emergency")) {
        urgency = "high";
        confidence = Math.min(confidence + 0.1, 1.0);
      }
      
      // Extract entities (simplified)
      const entities = [];
      
      // Email addresses
      const emailMatches = text.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g);
      if (emailMatches) {
        entities.push(...emailMatches.map((email: string) => ({
          type: "email",
          value: email,
          confidence: 0.95
        })));
      }
      
      // Phone numbers (simple pattern)
      const phoneMatches = text.match(/\b\d{3}-\d{3}-\d{4}\b/g);
      if (phoneMatches) {
        entities.push(...phoneMatches.map((phone: string) => ({
          type: "phone",
          value: phone,
          confidence: 0.9
        })));
      }
      
      // Company names (detect @company patterns)
      const companyMatches = text.match(/@([a-zA-Z0-9.-]+)\./g);
      if (companyMatches) {
        entities.push(...companyMatches.map((company: string) => ({
          type: "company",
          value: company.replace(/@|\..*$/g, ""),
          confidence: 0.8
        })));
      }
      
      // Generate suggested actions based on intent
      const suggestedActions = [];
      switch (intent) {
        case "schedule_meeting":
          suggestedActions.push("Reply with available time slots");
          suggestedActions.push("Create calendar event");
          suggestedActions.push("Forward to scheduling assistant");
          break;
        case "complaint":
          suggestedActions.push("Escalate to customer service");
          suggestedActions.push("Reply with acknowledgment");
          suggestedActions.push("Create support ticket");
          break;
        case "billing_inquiry":
          suggestedActions.push("Forward to accounting department");
          suggestedActions.push("Reply with payment information");
          suggestedActions.push("Create billing record");
          break;
        case "job_application":
          suggestedActions.push("Forward to HR department");
          suggestedActions.push("Reply with acknowledgment");
          suggestedActions.push("Save to candidate database");
          break;
        default:
          suggestedActions.push("Reply with relevant information");
          suggestedActions.push("File for reference");
          break;
      }
      
      const result = {
        intent,
        confidence,
        category,
        entities,
        urgency,
        requiresResponse,
        suggestedActions
      };
      
      res.json(result);
      
    } catch (error) {
      console.error("Error analyzing intent:", error);
      res.status(500).json({ error: "Failed to analyze intent" });
    }
  });

  // User Settings API
  app.get("/api/user/settings", async (req, res) => {
    try {
      const userId = 1; // Mock user ID for development
      const user = await storage.getUser(userId);
      
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }
      
      res.json({
        id: user.id,
        username: user.username,
        email: user.email,
        defaultLocation: user.defaultLocation || "",
        workingHours: user.workingHours || {
          start: "09:00",
          end: "17:00", 
          timezone: "America/New_York",
          workDays: [1, 2, 3, 4, 5]
        }
      });
    } catch (error) {
      console.error("Error fetching user settings:", error);
      res.status(500).json({ error: "Failed to fetch user settings" });
    }
  });

  app.patch("/api/user/settings", async (req, res) => {
    try {
      const userId = 1; // Mock user ID for development
      const { defaultLocation, workingHours, username, email } = req.body;
      
      // In production, validate the user can update these settings
      const updateData: any = {};
      if (defaultLocation !== undefined) updateData.defaultLocation = defaultLocation;
      if (workingHours !== undefined) updateData.workingHours = workingHours;
      if (username !== undefined) updateData.username = username;
      if (email !== undefined) updateData.email = email;
      
      const updatedUser = await storage.updateUser(userId, updateData);
      res.json({ 
        success: true, 
        message: "Settings updated successfully",
        user: updatedUser
      });
    } catch (error) {
      console.error("Error updating user settings:", error);
      res.status(500).json({ error: "Failed to update user settings" });
    }
  });

  // AI Meeting Scheduler APIs
  app.post("/api/ai/suggest-meeting-times", async (req, res) => {
    try {
      const { emailContent, senderEmail, duration, location } = req.body;
      const userId = 1; // Mock user ID for development
      
      const suggestions = await schedulerService.suggestMeetingFromEmail(
        userId,
        emailContent,
        senderEmail
      );
      
      res.json(suggestions);
    } catch (error) {
      console.error("Error suggesting meeting times:", error);
      res.status(500).json({ error: "Failed to suggest meeting times" });
    }
  });

  app.post("/api/ai/find-available-slots", async (req, res) => {
    try {
      const { duration, preferredTimes, location, buffer, searchDays } = req.body;
      const userId = 1; // Mock user ID for development
      
      const request = {
        duration: duration || 30,
        preferredTimes,
        location,
        buffer: buffer || 15
      };
      
      const slots = await schedulerService.findAvailableSlots(
        userId,
        request,
        searchDays || 14
      );
      
      res.json({ availableSlots: slots });
    } catch (error) {
      console.error("Error finding available slots:", error);
      res.status(500).json({ error: "Failed to find available slots" });
    }
  });

  app.post("/api/calendar/process-travel-times", async (req, res) => {
    try {
      const userId = 1; // Mock user ID for development
      
      await schedulerService.processAllUserEvents(userId);
      
      res.json({ 
        success: true, 
        message: "Travel times calculated for all calendar events" 
      });
    } catch (error) {
      console.error("Error processing travel times:", error);
      res.status(500).json({ error: "Failed to process travel times" });
    }
  });

  app.post("/api/calendar/check-conflicts", async (req, res) => {
    try {
      const { startTime, endTime, location } = req.body;
      const userId = 1; // Mock user ID for development
      
      const conflicts = await schedulerService.checkConflicts(userId, {
        startTime: new Date(startTime),
        endTime: new Date(endTime),
        location
      });
      
      res.json(conflicts);
    } catch (error) {
      console.error("Error checking conflicts:", error);
      res.status(500).json({ error: "Failed to check conflicts" });
    }
  });

  app.post("/api/calendar/calculate-travel-time", async (req, res) => {
    try {
      const { fromLocation, toLocation, mode } = req.body;
      
      const travelInfo = await schedulerService.calculateTravelTime(
        fromLocation,
        toLocation,
        mode || 'driving'
      );
      
      res.json(travelInfo);
    } catch (error) {
      console.error("Error calculating travel time:", error);
      res.status(500).json({ error: "Failed to calculate travel time" });
    }
  });

  // Email Condition Evaluation API
  app.post("/api/ai/evaluate-conditions", async (req, res) => {
    try {
      const { email, conditionGroups } = req.body;
      
      // Simulate condition evaluation
      const evaluateCondition = (condition: any, email: any) => {
        const { type, operator, value: conditionValue, caseSensitive } = condition;
        
        if (!condition.enabled) return false;
        
        let emailValue = "";
        switch (type) {
          case "sender":
            emailValue = email.sender || "";
            break;
          case "subject":
            emailValue = email.subject || "";
            break;
          case "body":
            emailValue = email.body || "";
            break;
          case "intent":
            emailValue = email.intent || "";
            break;
          case "urgency":
            emailValue = email.urgency || "";
            break;
          case "entities":
            emailValue = email.entities ? email.entities.join(" ") : "";
            break;
          case "attachment":
            return email.attachments && email.attachments.length > 0;
          default:
            return false;
        }
        
        let compareValue = conditionValue;
        if (!caseSensitive) {
          emailValue = emailValue.toLowerCase();
          compareValue = conditionValue.toLowerCase();
        }
        
        switch (operator) {
          case "equals":
            return emailValue === compareValue;
          case "contains":
            return emailValue.includes(compareValue);
          case "startsWith":
            return emailValue.startsWith(compareValue);
          case "endsWith":
            return emailValue.endsWith(compareValue);
          case "matches":
            try {
              const regex = new RegExp(compareValue);
              return regex.test(emailValue);
            } catch {
              return false;
            }
          case "in":
            return compareValue.split(",").map((v: string) => v.trim()).includes(emailValue);
          case "notIn":
            return !compareValue.split(",").map((v: string) => v.trim()).includes(emailValue);
          default:
            return false;
        }
      };
      
      const results = conditionGroups.map((group: any) => {
        const conditionResults = group.conditions.map((condition: any) => ({
          conditionId: condition.id,
          result: evaluateCondition(condition, email)
        }));
        
        const groupResult = group.logic === "AND" 
          ? conditionResults.every((r: any) => r.result)
          : conditionResults.some((r: any) => r.result);
          
        return {
          groupId: group.id,
          result: groupResult,
          conditions: conditionResults
        };
      });
      
      // Overall result - any group must match
      const overallResult = results.some((r: any) => r.result);
      
      res.json({
        matches: overallResult,
        groups: results
      });
      
    } catch (error) {
      console.error("Error evaluating conditions:", error);
      res.status(500).json({ error: "Failed to evaluate conditions" });
    }
  });

  // AI Automations routes
  app.get("/api/ai-automations", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const automations = await storage.getAiAutomations(userId);
      res.json(automations);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch AI automations" });
    }
  });

  app.post("/api/ai-automations", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const automationData = insertAiAutomationSchema.parse({ ...req.body, userId });
      const automation = await storage.createAiAutomation(automationData);
      res.json(automation);
    } catch (error) {
      res.status(500).json({ error: "Failed to create AI automation" });
    }
  });

  app.patch("/api/ai-automations/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const automation = await storage.updateAiAutomation(id, req.body);
      res.json(automation);
    } catch (error) {
      res.status(500).json({ error: "Failed to update AI automation" });
    }
  });

  app.delete("/api/ai-automations/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteAiAutomation(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete AI automation" });
    }
  });

  // Inbox routes
  app.get("/api/inboxes", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      console.log('Fetching inboxes for userId:', userId);
      const inboxes = await storage.getInboxes(userId);
      console.log('Retrieved inboxes:', inboxes.length);
      res.json(inboxes);
    } catch (error) {
      console.error('Inbox API error:', error);
      res.status(500).json({ error: "Failed to fetch inboxes", details: error instanceof Error ? error.message : 'Unknown error' });
    }
  });

  app.post("/api/inboxes", async (req, res) => {
    try {
      const userId = getCurrentUserId();
      const inboxData = insertInboxSchema.parse({ ...req.body, userId });
      const inbox = await storage.createInbox(inboxData);
      res.json(inbox);
    } catch (error) {
      res.status(500).json({ error: "Failed to create inbox" });
    }
  });

  app.patch("/api/inboxes/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const inbox = await storage.updateInbox(id, req.body);
      res.json(inbox);
    } catch (error) {
      res.status(500).json({ error: "Failed to update inbox" });
    }
  });

  app.delete("/api/inboxes/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteInbox(id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: "Failed to delete inbox" });
    }
  });

  // Email Templates routes
  app.get('/api/email-templates', async (req, res) => {
    try {
      const templates = await storage.getEmailTemplates(1); // Mock user ID
      res.json(templates);
    } catch (error) {
      console.error('Error fetching email templates:', error);
      res.status(500).json({ error: 'Failed to fetch email templates' });
    }
  });

  app.get('/api/email-templates/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const template = await storage.getEmailTemplate(id);
      if (!template) {
        res.status(404).json({ error: 'Template not found' });
        return;
      }
      res.json(template);
    } catch (error) {
      console.error('Error fetching email template:', error);
      res.status(500).json({ error: 'Failed to fetch email template' });
    }
  });

  app.post('/api/email-templates', async (req, res) => {
    try {
      const templateData = insertEmailTemplateSchema.parse({
        ...req.body,
        userId: 1 // Mock user ID
      });
      const template = await storage.createEmailTemplate(templateData);
      res.json(template);
    } catch (error) {
      console.error('Error creating email template:', error);
      res.status(500).json({ error: 'Failed to create email template' });
    }
  });

  app.patch('/api/email-templates/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updateData = insertEmailTemplateSchema.partial().parse(req.body);
      const template = await storage.updateEmailTemplate(id, updateData);
      res.json(template);
    } catch (error) {
      console.error('Error updating email template:', error);
      res.status(500).json({ error: 'Failed to update email template' });
    }
  });

  app.delete('/api/email-templates/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteEmailTemplate(id);
      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting email template:', error);
      res.status(500).json({ error: 'Failed to delete email template' });
    }
  });

  app.get('/api/email-templates/category/:category', async (req, res) => {
    try {
      const category = req.params.category;
      const templates = await storage.getTemplatesByCategory(1, category); // Mock user ID
      res.json(templates);
    } catch (error) {
      console.error('Error fetching templates by category:', error);
      res.status(500).json({ error: 'Failed to fetch templates by category' });
    }
  });

  app.get('/api/email-templates/intent/:intent', async (req, res) => {
    try {
      const intent = req.params.intent;
      const templates = await storage.getTemplatesByIntent(1, intent); // Mock user ID
      res.json(templates);
    } catch (error) {
      console.error('Error fetching templates by intent:', error);
      res.status(500).json({ error: 'Failed to fetch templates by intent' });
    }
  });

  // Email Folders routes
  app.get('/api/email-folders', async (req, res) => {
    try {
      const folders = await storage.getEmailFolders(1); // Mock user ID
      res.json(folders);
    } catch (error) {
      console.error('Error fetching email folders:', error);
      res.status(500).json({ error: 'Failed to fetch email folders' });
    }
  });

  app.post('/api/email-folders', async (req, res) => {
    try {
      const folderData = insertEmailFolderSchema.parse({
        ...req.body,
        userId: 1 // Mock user ID
      });
      const folder = await storage.createEmailFolder(folderData);
      res.json(folder);
    } catch (error) {
      console.error('Error creating email folder:', error);
      res.status(500).json({ error: 'Failed to create email folder' });
    }
  });

  app.patch('/api/email-folders/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updateData = insertEmailFolderSchema.partial().parse(req.body);
      const folder = await storage.updateEmailFolder(id, updateData);
      res.json(folder);
    } catch (error) {
      console.error('Error updating email folder:', error);
      res.status(500).json({ error: 'Failed to update email folder' });
    }
  });

  app.delete('/api/email-folders/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteEmailFolder(id);
      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting email folder:', error);
      res.status(500).json({ error: 'Failed to delete email folder' });
    }
  });

  // Email Workflows routes
  app.get('/api/email-workflows', async (req, res) => {
    try {
      const workflows = await storage.getEmailWorkflows(1); // Mock user ID
      res.json(workflows);
    } catch (error) {
      console.error('Error fetching email workflows:', error);
      res.status(500).json({ error: 'Failed to fetch email workflows' });
    }
  });

  app.post('/api/email-workflows', async (req, res) => {
    try {
      const workflowData = insertEmailWorkflowSchema.parse({
        ...req.body,
        userId: 1 // Mock user ID
      });
      const workflow = await storage.createEmailWorkflow(workflowData);
      res.json(workflow);
    } catch (error) {
      console.error('Error creating email workflow:', error);
      res.status(500).json({ error: 'Failed to create email workflow' });
    }
  });

  app.patch('/api/email-workflows/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updateData = insertEmailWorkflowSchema.partial().parse(req.body);
      const workflow = await storage.updateEmailWorkflow(id, updateData);
      res.json(workflow);
    } catch (error) {
      console.error('Error updating email workflow:', error);
      res.status(500).json({ error: 'Failed to update email workflow' });
    }
  });

  app.delete('/api/email-workflows/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteEmailWorkflow(id);
      res.json({ success: true });
    } catch (error) {
      console.error('Error deleting email workflow:', error);
      res.status(500).json({ error: 'Failed to delete email workflow' });
    }
  });

  app.get('/api/email-workflows/active', async (req, res) => {
    try {
      const workflows = await storage.getActiveWorkflows(1); // Mock user ID
      res.json(workflows);
    } catch (error) {
      console.error('Error fetching active workflows:', error);
      res.status(500).json({ error: 'Failed to fetch active workflows' });
    }
  });

  // Execute workflow attachment processing
  app.post("/api/workflows/:id/process-attachments", async (req, res) => {
    try {
      const workflowId = parseInt(req.params.id);
      const { emailId, attachments } = req.body;

      const workflow = await storage.getEmailWorkflow(workflowId);
      if (!workflow) {
        return res.status(404).json({ error: "Workflow not found" });
      }

      // Find attachment processing action
      const attachmentAction = workflow.actions?.find(action => action.type === "process_attachments");
      if (!attachmentAction) {
        return res.status(400).json({ error: "Workflow does not contain attachment processing action" });
      }

      // Mock processing results based on configuration
      const results = attachments.map((attachment: any) => {
        const config = attachmentAction.config;
        
        switch (config.processingMode) {
          case "smart_routing":
            return {
              fileName: attachment.name,
              destination: config.defaultDestination || "archive",
              confidence: parseFloat(config.confidenceThreshold) || 0.7,
              status: "routed"
            };
          
          case "extract_data":
            const extractedData: any = {};
            if (config.extractTypes?.includes('dates')) extractedData.dates = ["2024-12-31"];
            if (config.extractTypes?.includes('amounts')) extractedData.amounts = ["$1,250.00"];
            if (config.extractTypes?.includes('names')) extractedData.names = ["John Smith"];
            return {
              fileName: attachment.name,
              extractedData,
              status: "processed"
            };
          
          case "create_tasks":
            return {
              fileName: attachment.name,
              taskCreated: {
                title: config.taskTemplate?.replace('{{filename}}', attachment.name) || `Process ${attachment.name}`,
                assignedTo: config.assignTo || "unassigned"
              },
              status: "task_created"
            };
          
          default:
            return {
              fileName: attachment.name,
              status: "categorized",
              category: "general"
            };
        }
      });

      // Update workflow execution count - just mock the success for now
      console.log(`Workflow ${workflowId} executed successfully`);

      res.json({
        workflowId,
        emailId,
        results,
        processed: true
      });

    } catch (error) {
      console.error("Error processing attachments:", error);
      res.status(500).json({ error: "Failed to process attachments" });
    }
  });

  return httpServer;
}

export { wsService };
