import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Attachment<PERSON>and<PERSON> from "@/components/email/attachment-handler";
import { FileText, Mail, ArrowLeft } from "lucide-react";

export default function AttachmentDemo() {
  const [selectedEmail, setSelectedEmail] = useState<number | null>(null);

  const mockEmails = [
    {
      id: 1,
      subject: "Q4 Invoice - Project Alpha",
      sender: "<EMAIL>",
      attachments: [
        {
          id: "inv_001",
          name: "Invoice_Q4_2024_ProjectAlpha.pdf",
          type: "application/pdf",
          size: 145600
        },
        {
          id: "receipt_001",
          name: "Receipt_December.pdf",
          type: "application/pdf",
          size: 89234
        }
      ]
    },
    {
      id: 2,
      subject: "Resume Submission - Senior Developer Role",
      sender: "<EMAIL>",
      attachments: [
        {
          id: "resume_001",
          name: "<PERSON>_Doe_Resume_2024.pdf",
          type: "application/pdf",
          size: 234567
        },
        {
          id: "portfolio_001",
          name: "Portfolio_Projects.pdf",
          type: "application/pdf",
          size: 1456789
        }
      ]
    },
    {
      id: 3,
      subject: "Contract Review - Vendor Agreement",
      sender: "<EMAIL>",
      attachments: [
        {
          id: "contract_001",
          name: "Vendor_Service_Agreement_2024.pdf",
          type: "application/pdf",
          size: 456789
        },
        {
          id: "terms_001",
          name: "Terms_and_Conditions.docx",
          type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          size: 123456
        }
      ]
    }
  ];

  if (selectedEmail !== null) {
    const email = mockEmails.find(e => e.id === selectedEmail)!;
    return (
      <div className="p-6 space-y-6 max-w-4xl mx-auto">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => setSelectedEmail(null)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Email List
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Smart Attachment Processing Demo
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Processing attachments from: {email.subject}
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              {email.subject}
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              From: {email.sender}
            </p>
          </CardHeader>
          <CardContent>
            <div className="prose dark:prose-invert max-w-none mb-6">
              <p>
                This is a sample email body. In a real application, this would contain the actual email content.
                The AI attachment handler below will analyze the attachments and suggest intelligent routing options.
              </p>
            </div>
          </CardContent>
        </Card>

        <AttachmentHandler 
          attachments={email.attachments}
          emailId={email.id}
          emailSubject={email.subject}
          senderEmail={email.sender}
        />

        <Card className="bg-blue-50 dark:bg-blue-900/20">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-3">
              How Smart Attachment Processing Works:
            </h3>
            <div className="space-y-2 text-sm text-blue-800 dark:text-blue-300">
              <div className="flex items-start space-x-2">
                <span className="font-medium">1.</span>
                <span>Click "Smart Route" to analyze attachments with AI</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium">2.</span>
                <span>AI suggests destinations based on file type, email context, and learned patterns</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium">3.</span>
                <span>Review and customize the suggested routing for each attachment</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium">4.</span>
                <span>Process all attachments with one click - forward, save, or extract data</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium">5.</span>
                <span>Optional automatic reply confirms receipt to the sender</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Attachment Handling</h1>
      </div>
      <div className="grid gap-4">
        {mockEmails.map(email => (
          <Card key={email.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                    {email.subject}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    From: {email.sender}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      <FileText className="w-3 h-3 mr-1" />
                      {email.attachments.length} attachments
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {email.attachments.map(a => a.name).join(", ")}
                    </span>
                  </div>
                </div>
                <Button onClick={() => setSelectedEmail(email.id)}>
                  Process Attachments
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      <Card className="bg-green-50 dark:bg-green-900/20">
        <CardContent className="p-6">
          <h3 className="font-semibold text-green-900 dark:text-green-200 mb-3">
            Smart Destination Routing Features:
          </h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-green-800 dark:text-green-300">
            <div>
              <h4 className="font-medium mb-2">Intelligent Analysis:</h4>
              <ul className="space-y-1">
                <li>• File type detection (PDF, Excel, Word, etc.)</li>
                <li>• Content context analysis</li>
                <li>• Sender pattern recognition</li>
                <li>• Subject line interpretation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Smart Actions:</h4>
              <ul className="space-y-1">
                <li>• Forward to appropriate departments</li>
                <li>• Save to document archives</li>
                <li>• Extract data for processing</li>
                <li>• Send confirmation replies</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}