using EmailClient.Models;
using EmailClient.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace EmailClient.ViewModels;

public class ComposeEmailViewModel : INotifyPropertyChanged
{
    private readonly IEmailService _emailService;
    private string _toAddresses = string.Empty;
    private string _ccAddresses = string.Empty;
    private string _bccAddresses = string.Empty;
    private string _subject = string.Empty;
    private string _body = string.Empty;
    private string _priority = "Normal";
    private bool _showCcBcc = false;

    public event PropertyChangedEventHandler? PropertyChanged;

    public ComposeEmailViewModel(IEmailService emailService)
    {
        _emailService = emailService;
        Attachments = new ObservableCollection<EmailAttachment>();
    }

    public string ToAddresses
    {
        get => _toAddresses;
        set
        {
            _toAddresses = value;
            OnPropertyChanged();
        }
    }

    public string CcAddresses
    {
        get => _ccAddresses;
        set
        {
            _ccAddresses = value;
            OnPropertyChanged();
        }
    }

    public string BccAddresses
    {
        get => _bccAddresses;
        set
        {
            _bccAddresses = value;
            OnPropertyChanged();
        }
    }

    public string Subject
    {
        get => _subject;
        set
        {
            _subject = value;
            OnPropertyChanged();
        }
    }

    public string Body
    {
        get => _body;
        set
        {
            _body = value;
            OnPropertyChanged();
        }
    }

    public string Priority
    {
        get => _priority;
        set
        {
            _priority = value;
            OnPropertyChanged();
        }
    }

    public bool ShowCcBcc
    {
        get => _showCcBcc;
        set
        {
            _showCcBcc = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<EmailAttachment> Attachments { get; }

    public async Task<bool> SendEmailAsync()
    {
        try
        {
            var emailMessage = CreateEmailMessage();
            await _emailService.SendEmailAsync(emailMessage);
            return true;
        }
        catch (Exception ex)
        {
            // Log error
            Console.WriteLine($"Error sending email: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> SaveDraftAsync()
    {
        try
        {
            var emailMessage = CreateEmailMessage();
            emailMessage.IsDraft = true;
            await _emailService.SaveDraftAsync(emailMessage);
            return true;
        }
        catch (Exception ex)
        {
            // Log error
            Console.WriteLine($"Error saving draft: {ex.Message}");
            return false;
        }
    }

    public void AddAttachment(string filePath)
    {
        if (File.Exists(filePath))
        {
            var attachment = new EmailAttachment
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                FileSize = new FileInfo(filePath).Length
            };
            Attachments.Add(attachment);
        }
    }

    public void RemoveAttachment(EmailAttachment attachment)
    {
        Attachments.Remove(attachment);
    }

    public void SetReplyData(EmailMessage originalMessage)
    {
        ToAddresses = originalMessage.FromAddress;
        Subject = originalMessage.Subject.StartsWith("Re:") 
            ? originalMessage.Subject 
            : $"Re: {originalMessage.Subject}";
        
        Body = $"\n\n--- Original Message ---\n" +
               $"From: {originalMessage.FromDisplay}\n" +
               $"Date: {originalMessage.DateReceived:yyyy-MM-dd HH:mm}\n" +
               $"Subject: {originalMessage.Subject}\n\n" +
               $"{originalMessage.TextBody}";
    }

    public void SetForwardData(EmailMessage originalMessage)
    {
        Subject = originalMessage.Subject.StartsWith("Fwd:") 
            ? originalMessage.Subject 
            : $"Fwd: {originalMessage.Subject}";
        
        Body = $"\n\n--- Forwarded Message ---\n" +
               $"From: {originalMessage.FromDisplay}\n" +
               $"Date: {originalMessage.DateReceived:yyyy-MM-dd HH:mm}\n" +
               $"To: {originalMessage.ToAddress}\n" +
               $"Subject: {originalMessage.Subject}\n\n" +
               $"{originalMessage.TextBody}";

        // Copy attachments if any
        if (originalMessage.HasAttachments)
        {
            // TODO: Implement attachment copying
        }
    }

    public void Clear()
    {
        ToAddresses = string.Empty;
        CcAddresses = string.Empty;
        BccAddresses = string.Empty;
        Subject = string.Empty;
        Body = string.Empty;
        Priority = "Normal";
        ShowCcBcc = false;
        Attachments.Clear();
    }

    private EmailMessage CreateEmailMessage()
    {
        return new EmailMessage
        {
            ToAddress = ToAddresses,
            CcAddress = CcAddresses,
            BccAddress = BccAddresses,
            Subject = Subject,
            TextBody = Body,
            HtmlBody = ConvertToHtml(Body),
            Priority = Priority,
            DateSent = DateTime.Now,
            HasAttachments = Attachments.Count > 0,
            // TODO: Set sender from current account
            FromAddress = "<EMAIL>",
            FromName = "Current User"
        };
    }

    private string ConvertToHtml(string textBody)
    {
        // Simple text to HTML conversion
        var html = System.Net.WebUtility.HtmlEncode(textBody);
        html = html.Replace("\n", "<br>");
        return $"<html><body style='font-family: Arial, sans-serif; line-height: 1.6;'>{html}</body></html>";
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class EmailAttachment
{
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string FileSizeDisplay => FormatFileSize(FileSize);

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }
}
