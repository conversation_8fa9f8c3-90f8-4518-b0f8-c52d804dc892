using EmailClient.Models;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace EmailClient.Views.Controls;

public partial class AccountSwitcher : UserControl, INotifyPropertyChanged
{
    public static readonly DependencyProperty AccountsProperty =
        DependencyProperty.Register("Accounts", typeof(ObservableCollection<AccountSwitcherItem>), 
            typeof(AccountSwitcher), new PropertyMetadata(null));

    public static readonly DependencyProperty SelectedAccountIdProperty =
        DependencyProperty.Register("SelectedAccountId", typeof(int?), 
            typeof(AccountSwitcher), new PropertyMetadata(null, OnSelectedAccountChanged));

    public static readonly RoutedEvent AccountChangedEvent = 
        EventManager.RegisterRoutedEvent("AccountChanged", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(AccountSwitcher));

    public static readonly RoutedEvent AddAccountRequestedEvent = 
        EventManager.RegisterRoutedEvent("AddAccountRequested", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(AccountSwitcher));

    public event PropertyChangedEventHandler? PropertyChanged;
    public event RoutedEventHandler AccountChanged
    {
        add { AddHandler(AccountChangedEvent, value); }
        remove { RemoveHandler(AccountChangedEvent, value); }
    }

    public event RoutedEventHandler AddAccountRequested
    {
        add { AddHandler(AddAccountRequestedEvent, value); }
        remove { RemoveHandler(AddAccountRequestedEvent, value); }
    }

    public ObservableCollection<AccountSwitcherItem>? Accounts
    {
        get { return (ObservableCollection<AccountSwitcherItem>?)GetValue(AccountsProperty); }
        set { SetValue(AccountsProperty, value); }
    }

    public int? SelectedAccountId
    {
        get { return (int?)GetValue(SelectedAccountIdProperty); }
        set { SetValue(SelectedAccountIdProperty, value); }
    }

    public AccountSwitcher()
    {
        InitializeComponent();
        DataContext = this;
        
        // Initialize with sample data for design time
        if (DesignerProperties.GetIsInDesignMode(this))
        {
            Accounts = new ObservableCollection<AccountSwitcherItem>
            {
                new AccountSwitcherItem
                {
                    Id = 1,
                    Name = "Gmail Account",
                    Email = "<EMAIL>",
                    Provider = "Gmail",
                    IsConnected = true,
                    UnreadCount = 12,
                    StatusText = "<EMAIL> • 12 unread"
                },
                new AccountSwitcherItem
                {
                    Id = 2,
                    Name = "Outlook Account",
                    Email = "<EMAIL>",
                    Provider = "Outlook",
                    IsConnected = true,
                    UnreadCount = 5,
                    StatusText = "<EMAIL> • 5 unread"
                }
            };
        }
    }

    private static void OnSelectedAccountChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is AccountSwitcher control)
        {
            control.UpdateDisplayedAccount();
        }
    }

    private void AccountSwitcherButton_Click(object sender, RoutedEventArgs e)
    {
        AccountDropdown.IsOpen = !AccountDropdown.IsOpen;
        
        if (AccountDropdown.IsOpen)
        {
            DropdownArrow.Text = "⌃";
        }
        else
        {
            DropdownArrow.Text = "⌄";
        }
    }

    private void AccountOption_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string tag)
        {
            int? newAccountId = null;
            
            if (tag != "unified" && int.TryParse(tag, out int accountId))
            {
                newAccountId = accountId;
            }

            SelectedAccountId = newAccountId;
            AccountDropdown.IsOpen = false;
            DropdownArrow.Text = "⌄";

            var args = new AccountChangedEventArgs(AccountChangedEvent, this)
            {
                SelectedAccountId = newAccountId
            };
            RaiseEvent(args);
        }
    }

    private void AddAccountButton_Click(object sender, RoutedEventArgs e)
    {
        AccountDropdown.IsOpen = false;
        DropdownArrow.Text = "⌄";
        
        var args = new RoutedEventArgs(AddAccountRequestedEvent, this);
        RaiseEvent(args);
    }

    private void UpdateDisplayedAccount()
    {
        if (SelectedAccountId == null)
        {
            // Unified inbox
            AccountNameText.Text = "Unified Inbox";
            AccountInfoText.Text = GetUnifiedInboxInfo();
            StatusIndicator.Background = (Brush)FindResource("SuccessBrush");
            
            // Update unified inbox button state
            UnifiedInboxButton.Background = (Brush)FindResource("PrimaryLightBrush");
        }
        else
        {
            // Specific account
            var account = Accounts?.FirstOrDefault(a => a.Id == SelectedAccountId);
            if (account != null)
            {
                AccountNameText.Text = account.Name;
                AccountInfoText.Text = account.StatusText;
                StatusIndicator.Background = account.StatusColor;
            }
            
            // Reset unified inbox button state
            UnifiedInboxButton.Background = Brushes.Transparent;
        }
    }

    private string GetUnifiedInboxInfo()
    {
        if (Accounts == null || !Accounts.Any())
            return "No accounts connected";

        var connectedCount = Accounts.Count(a => a.IsConnected);
        var totalUnread = Accounts.Where(a => a.IsConnected).Sum(a => a.UnreadCount);
        
        return $"All accounts • {connectedCount} connected • {totalUnread} unread";
    }

    public void UpdateUnifiedInboxInfo()
    {
        if (SelectedAccountId == null)
        {
            AccountInfoText.Text = GetUnifiedInboxInfo();
            UnifiedInboxInfo.Text = GetUnifiedInboxInfo();
        }
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class AccountSwitcherItem : INotifyPropertyChanged
{
    private bool _isConnected;
    private int _unreadCount;
    private string _statusText = string.Empty;

    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    
    public bool IsConnected
    {
        get => _isConnected;
        set
        {
            _isConnected = value;
            OnPropertyChanged(nameof(IsConnected));
            OnPropertyChanged(nameof(StatusColor));
        }
    }
    
    public int UnreadCount
    {
        get => _unreadCount;
        set
        {
            _unreadCount = value;
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnread));
        }
    }
    
    public string StatusText
    {
        get => _statusText;
        set
        {
            _statusText = value;
            OnPropertyChanged(nameof(StatusText));
        }
    }

    public bool HasUnread => UnreadCount > 0;
    
    public Brush StatusColor => IsConnected 
        ? (Brush)Application.Current.FindResource("SuccessBrush")
        : (Brush)Application.Current.FindResource("ErrorBrush");
    
    public string ProviderIcon => Provider switch
    {
        "Gmail" => "📧",
        "Outlook" => "📮",
        "Yahoo" => "💌",
        "Exchange" => "🏢",
        _ => "📬"
    };

    public event PropertyChangedEventHandler? PropertyChanged;
    
    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class AccountChangedEventArgs : RoutedEventArgs
{
    public int? SelectedAccountId { get; set; }

    public AccountChangedEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
    {
    }
}
