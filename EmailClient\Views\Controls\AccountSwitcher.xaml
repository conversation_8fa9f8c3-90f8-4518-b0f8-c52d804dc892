<UserControl x:Class="EmailClient.Views.Controls.AccountSwitcher"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="280">
    
    <Grid>
        <!-- Account Switcher Button -->
        <Button x:Name="AccountSwitcherButton"
                Style="{StaticResource ModernSecondaryButtonStyle}"
                HorizontalAlignment="Stretch"
                HorizontalContentAlignment="Left"
                Padding="12,8"
                Click="AccountSwitcherButton_Click">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Border Grid.Column="0" 
                        x:Name="StatusIndicator"
                        Width="8" Height="8" 
                        Background="{StaticResource SuccessBrush}" 
                        CornerRadius="4"
                        Margin="0,0,8,0"/>
                
                <StackPanel Grid.Column="1">
                    <TextBlock x:Name="AccountNameText"
                               Text="Unified Inbox" 
                               Style="{StaticResource BodyTextStyle}"
                               FontWeight="Medium"/>
                    <TextBlock x:Name="AccountInfoText"
                               Text="All accounts • 3 connected" 
                               Style="{StaticResource CaptionTextStyle}"/>
                </StackPanel>
                
                <TextBlock Grid.Column="2" 
                           x:Name="DropdownArrow"
                           Text="⌄" 
                           Style="{StaticResource CaptionTextStyle}"
                           VerticalAlignment="Center"/>
            </Grid>
        </Button>
        
        <!-- Account Dropdown Popup -->
        <Popup x:Name="AccountDropdown"
               PlacementTarget="{Binding ElementName=AccountSwitcherButton}"
               Placement="Bottom"
               AllowsTransparency="True"
               StaysOpen="False"
               PopupAnimation="Slide">
            <Border Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    CornerRadius="8"
                    Padding="8"
                    MinWidth="280"
                    MaxHeight="400"
                    Effect="{StaticResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Header -->
                    <TextBlock Grid.Row="0" 
                               Text="Switch Account" 
                               Style="{StaticResource HeadingSmallStyle}"
                               Margin="8,8,8,12"/>
                    
                    <!-- Unified Inbox Option -->
                    <Button Grid.Row="1"
                            x:Name="UnifiedInboxButton"
                            Style="{StaticResource ModernIconButtonStyle}"
                            HorizontalAlignment="Stretch"
                            HorizontalContentAlignment="Left"
                            Padding="12,8"
                            Margin="0,0,0,8"
                            Background="{StaticResource PrimaryLightBrush}"
                            Click="AccountOption_Click"
                            Tag="unified">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" 
                                       Text="🌐" 
                                       FontSize="16" 
                                       Margin="0,0,12,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Unified Inbox" 
                                           Style="{StaticResource BodyTextStyle}"
                                           FontWeight="Medium"/>
                                <TextBlock x:Name="UnifiedInboxInfo"
                                           Text="All accounts • 24 emails" 
                                           Style="{StaticResource CaptionTextStyle}"/>
                            </StackPanel>
                            
                            <TextBlock Grid.Column="2" 
                                       Text="✓" 
                                       Style="{StaticResource BodyTextStyle}"
                                       Foreground="{StaticResource PrimaryBrush}"
                                       FontWeight="Bold"/>
                        </Grid>
                    </Button>
                    
                    <!-- Account List -->
                    <ScrollViewer Grid.Row="2" 
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Disabled"
                                  MaxHeight="200">
                        <ItemsControl x:Name="AccountsList" 
                                      ItemsSource="{Binding Accounts}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource ModernIconButtonStyle}"
                                            HorizontalAlignment="Stretch"
                                            HorizontalContentAlignment="Left"
                                            Padding="12,8"
                                            Margin="0,2"
                                            Click="AccountOption_Click"
                                            Tag="{Binding Id}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <!-- Provider Icon -->
                                            <Border Grid.Column="0" 
                                                    Width="24" Height="24" 
                                                    Background="{StaticResource BorderBrush}" 
                                                    CornerRadius="12"
                                                    Margin="0,0,12,0">
                                                <TextBlock Text="{Binding ProviderIcon}" 
                                                           FontSize="12" 
                                                           HorizontalAlignment="Center" 
                                                           VerticalAlignment="Center"/>
                                            </Border>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" 
                                                           Style="{StaticResource BodyTextStyle}"
                                                           FontWeight="Medium"/>
                                                <TextBlock Text="{Binding StatusText}" 
                                                           Style="{StaticResource CaptionTextStyle}"/>
                                            </StackPanel>
                                            
                                            <!-- Status and Unread Count -->
                                            <StackPanel Grid.Column="2" 
                                                        Orientation="Horizontal"
                                                        VerticalAlignment="Center">
                                                <Border Width="8" Height="8" 
                                                        Background="{Binding StatusColor}" 
                                                        CornerRadius="4"
                                                        Margin="0,0,8,0"/>
                                                
                                                <Border Background="{StaticResource BorderBrush}" 
                                                        CornerRadius="10" 
                                                        Padding="6,2"
                                                        MinWidth="20"
                                                        Visibility="{Binding HasUnread, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                    <TextBlock Text="{Binding UnreadCount}" 
                                                               FontSize="11" 
                                                               FontWeight="Medium"
                                                               Foreground="{StaticResource TextSecondaryBrush}"
                                                               HorizontalAlignment="Center"/>
                                                </Border>
                                            </StackPanel>
                                        </Grid>
                                    </Button>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                    
                    <!-- Add Account Button -->
                    <Border Grid.Row="3" 
                            BorderBrush="{StaticResource BorderBrush}" 
                            BorderThickness="0,1,0,0"
                            Padding="0,8,0,0"
                            Margin="0,8,0,0">
                        <Button x:Name="AddAccountButton"
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                HorizontalAlignment="Stretch"
                                HorizontalContentAlignment="Left"
                                Padding="12,8"
                                Click="AddAccountButton_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="➕" 
                                           FontSize="16" 
                                           Margin="0,0,12,0"/>
                                <TextBlock Text="Add Account" 
                                           Style="{StaticResource BodyTextStyle}"/>
                            </StackPanel>
                        </Button>
                    </Border>
                </Grid>
            </Border>
        </Popup>
    </Grid>
</UserControl>
