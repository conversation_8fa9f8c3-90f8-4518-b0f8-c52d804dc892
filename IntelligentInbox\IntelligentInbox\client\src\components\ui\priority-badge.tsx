import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface PriorityBadgeProps {
  priority: "low" | "medium" | "high";
  className?: string;
}

export default function PriorityBadge({ priority, className }: PriorityBadgeProps) {
  const variants = {
    low: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    high: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  };

  const labels = {
    low: "Low",
    medium: "Normal",
    high: "High",
  };

  return (
    <Badge 
      variant="outline" 
      className={cn(
        "text-xs",
        variants[priority],
        className
      )}
    >
      {labels[priority]}
    </Badge>
  );
}