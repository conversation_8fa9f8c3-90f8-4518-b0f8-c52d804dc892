using EmailClient.Services;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace EmailClient.Views.Controls;

public partial class ThemeToggleButton : UserControl, INotifyPropertyChanged
{
    private IThemeService? _themeService;

    public event PropertyChangedEventHandler? PropertyChanged;

    public ThemeToggleButton()
    {
        InitializeComponent();
        DataContext = this;
        
        if (!DesignerProperties.GetIsInDesignMode(this))
        {
            Loaded += OnLoaded;
        }
    }

    private void OnLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            var app = Application.Current as App;
            _themeService = app?.ServiceProvider?.GetService<IThemeService>();
            
            if (_themeService != null)
            {
                _themeService.PropertyChanged += ThemeService_PropertyChanged;
                UpdateDisplay();
            }
        }
        catch
        {
            // Ignore errors in design mode or if service is not available
        }
    }

    private void ThemeService_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(IThemeService.CurrentTheme) || 
            e.PropertyName == nameof(IThemeService.IsDarkMode))
        {
            UpdateDisplay();
        }
    }

    private void ToggleButton_Click(object sender, RoutedEventArgs e)
    {
        _themeService?.ToggleTheme();
    }

    private void UpdateDisplay()
    {
        OnPropertyChanged(nameof(ShowLightIcon));
        OnPropertyChanged(nameof(ShowDarkIcon));
        OnPropertyChanged(nameof(ShowSystemIcon));
        OnPropertyChanged(nameof(TooltipText));
    }

    public bool ShowLightIcon => _themeService?.CurrentTheme == AppTheme.Light;
    public bool ShowDarkIcon => _themeService?.CurrentTheme == AppTheme.Dark;
    public bool ShowSystemIcon => _themeService?.CurrentTheme == AppTheme.System;

    public string TooltipText
    {
        get
        {
            if (_themeService == null) return "Toggle Theme";
            
            return _themeService.CurrentTheme switch
            {
                AppTheme.Light => "Switch to Dark Mode",
                AppTheme.Dark => "Switch to Light Mode",
                AppTheme.System => _themeService.IsSystemDarkMode() 
                    ? "Switch to Light Mode (Currently following system: Dark)" 
                    : "Switch to Dark Mode (Currently following system: Light)",
                _ => "Toggle Theme"
            };
        }
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected override void OnUnloaded(RoutedEventArgs e)
    {
        if (_themeService != null)
        {
            _themeService.PropertyChanged -= ThemeService_PropertyChanged;
        }
        base.OnUnloaded(e);
    }
}
