using EmailClient.ViewModels;
using EmailClient.Views.Controls;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace EmailClient.Views;

public partial class MainWindow : Window
{
    private MainWindowViewModel? _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        _viewModel = viewModel;

        // Subscribe to property changes to update email body
        _viewModel.PropertyChanged += ViewModel_PropertyChanged;
    }

    private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(MainWindowViewModel.SelectedMessage))
        {
            // The ModernEmailDetail control will handle the update automatically through binding
            EmailDetail?.RefreshContent();
        }
    }



    private void SearchBar_SearchRequested(object sender, RoutedEventArgs e)
    {
        if (e is SearchEventArgs searchArgs && _viewModel != null)
        {
            _viewModel.SearchQuery = searchArgs.SearchQuery;
            _viewModel.SearchCommand.Execute(null);
        }
    }

    private void SearchBar_SearchCleared(object sender, RoutedEventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.SearchQuery = string.Empty;
            _viewModel.SearchCommand.Execute(null);
        }
    }

    private void FilterBar_FilterChanged(object sender, RoutedEventArgs e)
    {
        if (e is FilterChangedEventArgs filterArgs && _viewModel != null)
        {
            // TODO: Implement filter functionality in ViewModel
            _viewModel.StatusMessage = $"Filter changed to: {filterArgs.SelectedFilter}";

            // Update filter count based on current emails
            var count = filterArgs.SelectedFilter switch
            {
                "Unread" => _viewModel.EmailMessages.Count(email => !email.IsRead),
                "Important" => _viewModel.EmailMessages.Count(email => email.IsFlagged),
                "Attachments" => _viewModel.EmailMessages.Count(email => email.HasAttachments),
                _ => _viewModel.EmailMessages.Count
            };

            FilterBar.UpdateCount(count);
        }
    }

    private void Sidebar_NavigationRequested(object sender, RoutedEventArgs e)
    {
        if (e is NavigationEventArgs navArgs && _viewModel != null)
        {
            switch (navArgs.NavigationTarget)
            {
                case "Inbox":
                    // Already on inbox - could refresh or do nothing
                    break;
                case "Contacts":
                    _viewModel.ShowContactsCommand.Execute(null);
                    break;
                case "Calendar":
                    _viewModel.ShowCalendarCommand.Execute(null);
                    break;
                case "Tasks":
                    // TODO: Implement tasks view
                    break;
                case "Documents":
                    // TODO: Implement documents view
                    break;
                case "AIInsights":
                    // TODO: Implement AI insights view
                    break;
                case "AIAutomations":
                    // TODO: Implement AI automations view
                    break;
            }
        }
    }

    private void Sidebar_AccountSwitchRequested(object sender, RoutedEventArgs e)
    {
        if (e is AccountSwitchEventArgs accountArgs && _viewModel != null)
        {
            // Handle account switching
            if (accountArgs.SelectedAccountId.HasValue)
            {
                // Switch to specific account
                // TODO: Implement account-specific view
                _viewModel.StatusMessage = $"Switched to account {accountArgs.SelectedAccountId}";
            }
            else
            {
                // Switch to unified inbox
                _viewModel.StatusMessage = "Switched to Unified Inbox";
                // TODO: Load unified inbox
            }
        }
        else if (_viewModel != null)
        {
            // Fallback - show account dashboard for add account requests
            _viewModel.ShowDashboardCommand.Execute(null);
        }
    }

    private void Sidebar_SettingsRequested(object sender, RoutedEventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.SettingsCommand.Execute(null);
        }
    }

    private void EmailDetail_EmailAction(object sender, RoutedEventArgs e)
    {
        if (e is EmailActionEventArgs actionArgs && _viewModel != null)
        {
            switch (actionArgs.Action)
            {
                case "Star":
                    // TODO: Implement star/unstar functionality
                    _viewModel.StatusMessage = $"Email {(actionArgs.Email?.IsFlagged == true ? "unstarred" : "starred")}";
                    break;
                default:
                    break;
            }
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }
        base.OnClosed(e);
    }
}
