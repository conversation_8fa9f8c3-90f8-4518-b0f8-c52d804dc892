using EmailClient.ViewModels;
using EmailClient.Views.Controls;
using Microsoft.Extensions.DependencyInjection;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace EmailClient.Views;

public partial class MainWindow : Window
{
    private MainWindowViewModel? _viewModel;

    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        _viewModel = viewModel;

        // Subscribe to property changes to update email body
        _viewModel.PropertyChanged += ViewModel_PropertyChanged;
    }

    private void ViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(MainWindowViewModel.SelectedMessage))
        {
            UpdateEmailBody();
        }
    }

    private void UpdateEmailBody()
    {
        if (_viewModel?.SelectedMessage == null)
        {
            EmailBodyBrowser.NavigateToString("<html><body></body></html>");
            return;
        }

        var message = _viewModel.SelectedMessage;
        var htmlContent = !string.IsNullOrEmpty(message.HtmlBody)
            ? message.HtmlBody
            : $"<html><body><pre>{System.Net.WebUtility.HtmlEncode(message.TextBody)}</pre></body></html>";

        EmailBodyBrowser.NavigateToString(htmlContent);
    }



    private void SearchBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter && DataContext is MainWindowViewModel viewModel)
        {
            viewModel.SearchCommand.Execute(null);
        }
    }

    private void Sidebar_NavigationRequested(object sender, RoutedEventArgs e)
    {
        if (e is NavigationEventArgs navArgs && _viewModel != null)
        {
            switch (navArgs.NavigationTarget)
            {
                case "Inbox":
                    // Already on inbox - could refresh or do nothing
                    break;
                case "Contacts":
                    _viewModel.ShowContactsCommand.Execute(null);
                    break;
                case "Calendar":
                    _viewModel.ShowCalendarCommand.Execute(null);
                    break;
                case "Tasks":
                    // TODO: Implement tasks view
                    break;
                case "Documents":
                    // TODO: Implement documents view
                    break;
                case "AIInsights":
                    // TODO: Implement AI insights view
                    break;
                case "AIAutomations":
                    // TODO: Implement AI automations view
                    break;
            }
        }
    }

    private void Sidebar_AccountSwitchRequested(object sender, RoutedEventArgs e)
    {
        // TODO: Implement account switcher popup/dropdown
        if (_viewModel != null)
        {
            // For now, just show the account dashboard
            _viewModel.ShowDashboardCommand.Execute(null);
        }
    }

    private void Sidebar_SettingsRequested(object sender, RoutedEventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.SettingsCommand.Execute(null);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        if (_viewModel != null)
        {
            _viewModel.PropertyChanged -= ViewModel_PropertyChanged;
        }
        base.OnClosed(e);
    }
}
