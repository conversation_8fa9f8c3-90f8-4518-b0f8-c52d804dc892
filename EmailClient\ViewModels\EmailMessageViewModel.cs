using EmailClient.Models;
using System.Windows;

namespace EmailClient.ViewModels;

public class EmailMessageViewModel : ViewModelBase
{
    private readonly EmailMessage _message;

    public EmailMessageViewModel(EmailMessage message)
    {
        _message = message;
    }

    public int Id => _message.Id;
    public string MessageId => _message.MessageId;
    public string Subject => string.IsNullOrEmpty(_message.Subject) ? "(No Subject)" : _message.Subject;
    public string FromAddress => _message.FromAddress;
    public string FromName => _message.FromName;
    public string FromDisplay => string.IsNullOrEmpty(FromName) ? FromAddress : $"{FromName} <{FromAddress}>";
    public DateTime DateSent => _message.DateSent;
    public DateTime DateReceived => _message.DateReceived;
    public string DateDisplay => DateReceived.ToString("MM/dd/yyyy");
    public string TextBody => _message.TextBody;
    public string HtmlBody => _message.HtmlBody;
    public bool IsRead => _message.IsRead;
    public bool IsFlagged => _message.IsFlagged;
    public bool HasAttachments => _message.HasAttachments;
    public FontWeight FontWeight => IsRead ? FontWeights.Normal : FontWeights.Bold;

    // New properties for modern UI
    public string SenderInitial
    {
        get
        {
            if (!string.IsNullOrEmpty(FromName))
            {
                var parts = FromName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length >= 2)
                    return $"{parts[0][0]}{parts[1][0]}".ToUpper();
                return FromName[0].ToString().ToUpper();
            }
            return FromAddress.Length > 0 ? FromAddress[0].ToString().ToUpper() : "?";
        }
    }

    public string Preview
    {
        get
        {
            var text = !string.IsNullOrEmpty(TextBody) ? TextBody : HtmlBody;
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Remove HTML tags if present
            text = System.Text.RegularExpressions.Regex.Replace(text, "<.*?>", string.Empty);

            // Clean up whitespace
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ").Trim();

            // Return first 100 characters
            return text.Length > 100 ? text.Substring(0, 100) + "..." : text;
        }
    }

    public string StarIcon => IsFlagged ? "⭐" : "☆";

    public EmailMessage GetModel() => _message;
}
