﻿#pragma checksum "..\..\..\..\..\Views\Controls\ModernSidebar.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2AE100E13D07EF64125365BE3BEB2467F6873079"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using EmailClient.Views.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace EmailClient.Views.Controls {
    
    
    /// <summary>
    /// ModernSidebar
    /// </summary>
    public partial class ModernSidebar : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 58 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal EmailClient.Views.Controls.AccountSwitcher AccountSwitcher;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NavigationPanel;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InboxButton;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border InboxBadge;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ContactsButton;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TasksButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CalendarButton;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DocumentsButton;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/EmailClient;V1.0.0.0;component/views/controls/modernsidebar.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AccountSwitcher = ((EmailClient.Views.Controls.AccountSwitcher)(target));
            return;
            case 2:
            this.NavigationPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.InboxButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.InboxButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.InboxBadge = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.ContactsButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.ContactsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TasksButton = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.TasksButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CalendarButton = ((System.Windows.Controls.Button)(target));
            
            #line 179 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.CalendarButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DocumentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.DocumentsButton.Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 231 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 252 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NavigationButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 307 "..\..\..\..\..\Views\Controls\ModernSidebar.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

