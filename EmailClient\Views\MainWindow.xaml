<Window x:Class="EmailClient.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:EmailClient.Converters"
        xmlns:controls="clr-namespace:EmailClient.Views.Controls"
        Title="Email Client" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <local:InvertedNullToVisibilityConverter x:Key="InvertedNullToVisibilityConverter"/>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <!-- Modern Layout: Sidebar + Main Content -->
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="280" MinWidth="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Modern Sidebar -->
        <controls:ModernSidebar Grid.Column="0"
                                x:Name="Sidebar"
                                NavigationRequested="Sidebar_NavigationRequested"
                                AccountSwitchRequested="Sidebar_AccountSwitchRequested"
                                SettingsRequested="Sidebar_SettingsRequested"/>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header Bar -->
            <Border Grid.Row="0"
                    Background="{StaticResource SurfaceBrush}"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="0,0,0,1"
                    Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Side - Title and Info -->
                    <StackPanel Grid.Column="0" Orientation="Vertical">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <TextBlock Text="Unified Inbox"
                                       Style="{StaticResource HeadingLargeStyle}"/>
                        </StackPanel>
                        <TextBlock Text="All accounts • 3 connected"
                                   Style="{StaticResource CaptionTextStyle}"/>
                    </StackPanel>

                    <!-- Right Side - Actions -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Modern Search Bar -->
                        <controls:ModernSearchBar x:Name="SearchBar"
                                                  SearchQuery="{Binding SearchQuery}"
                                                  SearchRequested="SearchBar_SearchRequested"
                                                  SearchCleared="SearchBar_SearchCleared"
                                                  Margin="0,0,12,0"/>

                        <!-- Modern Filter Bar -->
                        <controls:ModernFilterBar x:Name="FilterBar"
                                                  SelectedFilter="All"
                                                  FilterCount="{Binding UnreadEmailCount}"
                                                  FilterChanged="FilterBar_FilterChanged"
                                                  Margin="0,0,12,0"/>

                        <!-- Action Buttons -->
                        <StackPanel Orientation="Horizontal">
                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                    Command="{Binding RefreshCommand}"
                                    ToolTip="Refresh"
                                    Margin="0,0,8,0">
                                <TextBlock Text="{StaticResource Icon.Refresh}"
                                           Style="{StaticResource ModernIconStyle}"
                                           FontSize="16"/>
                            </Button>
                            <Button Style="{StaticResource ModernButtonStyle}"
                                    Padding="12,8"
                                    Command="{Binding ComposeCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="{StaticResource Icon.Compose}"
                                               Style="{StaticResource ModernIconStyle}"
                                               Foreground="White"
                                               FontSize="14"
                                               Margin="0,0,8,0"/>
                                    <TextBlock Text="Compose" FontSize="14"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Main Content - Split View -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="400" MinWidth="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Email List Panel -->
                <Border Grid.Column="0"
                        Background="{StaticResource SurfaceBrush}"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="0,0,1,0">

                    <!-- Modern Email List -->
                    <ListView ItemsSource="{Binding EmailMessages}"
                              SelectedItem="{Binding SelectedMessage}"
                              Style="{StaticResource ModernListViewStyle}"
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="16,12"
                                        Background="Transparent"
                                        BorderThickness="0,0,0,1"
                                        BorderBrush="{StaticResource BorderLightBrush}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Avatar -->
                                        <Border Grid.Column="0"
                                                Width="40" Height="40"
                                                Background="{StaticResource BorderBrush}"
                                                CornerRadius="20"
                                                Margin="0,0,12,0">
                                            <TextBlock Text="{Binding SenderInitial}"
                                                       Style="{StaticResource BodyTextStyle}"
                                                       FontWeight="Medium"
                                                       HorizontalAlignment="Center"
                                                       VerticalAlignment="Center"/>
                                        </Border>

                                        <!-- Email Content -->
                                        <StackPanel Grid.Column="1">
                                            <Grid Margin="0,0,0,4">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0"
                                                           Text="{Binding FromDisplay}"
                                                           Style="{StaticResource BodyTextStyle}"
                                                           FontWeight="{Binding FontWeight}"
                                                           TextTrimming="CharacterEllipsis"/>

                                                <TextBlock Grid.Column="1"
                                                           Text="{Binding DateDisplay}"
                                                           Style="{StaticResource CaptionTextStyle}"
                                                           Margin="8,0,0,0"/>
                                            </Grid>

                                            <TextBlock Text="{Binding Subject}"
                                                       Style="{StaticResource BodyTextStyle}"
                                                       FontWeight="{Binding FontWeight}"
                                                       TextTrimming="CharacterEllipsis"
                                                       Margin="0,0,0,4"/>

                                            <TextBlock Text="{Binding Preview}"
                                                       Style="{StaticResource CaptionTextStyle}"
                                                       TextTrimming="CharacterEllipsis"
                                                       MaxHeight="32"
                                                       TextWrapping="Wrap"/>
                                        </StackPanel>

                                        <!-- Status Indicators -->
                                        <StackPanel Grid.Column="2"
                                                    Orientation="Vertical"
                                                    VerticalAlignment="Top"
                                                    Margin="8,0,0,0">
                                            <!-- Unread Indicator -->
                                            <Border Width="8" Height="8"
                                                    Background="{StaticResource PrimaryBrush}"
                                                    CornerRadius="4"
                                                    Visibility="{Binding IsRead, Converter={StaticResource BooleanToVisibilityConverter}}"
                                                    Margin="0,0,0,4"/>

                                            <!-- Star Button -->
                                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                                    Width="24" Height="24"
                                                    Padding="4">
                                                <TextBlock Text="⭐" FontSize="12"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                        <ListView.ItemContainerStyle>
                            <Style TargetType="ListViewItem" BasedOn="{StaticResource ModernListViewItemStyle}">
                                <Setter Property="Padding" Value="0"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsRead}" Value="False">
                                        <Setter Property="Background" Value="{StaticResource UnreadBrush}"/>
                                    </DataTrigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter Property="Background" Value="{StaticResource SelectedBrush}"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </ListView.ItemContainerStyle>
                    </ListView>
                </Border>

                <!-- Email Detail Panel -->
                <controls:ModernEmailDetail Grid.Column="1"
                                            x:Name="EmailDetail"
                                            SelectedEmail="{Binding SelectedMessage}"
                                            EmailAction="EmailDetail_EmailAction"/>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Height="32">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource CaptionTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding TotalEmailCount}"
                               Style="{StaticResource CaptionTextStyle}"
                               Margin="0,0,4,0"/>
                    <TextBlock Text="emails" Style="{StaticResource CaptionTextStyle}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
