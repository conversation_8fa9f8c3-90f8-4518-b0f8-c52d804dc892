@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(224, 71.4%, 4.1%);
  --muted: hsl(220, 14.3%, 95.9%);
  --muted-foreground: hsl(220, 8.9%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(224, 71.4%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(224, 71.4%, 4.1%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(220, 14.3%, 95.9%);
  --secondary-foreground: hsl(220, 8.9%, 46.1%);
  --accent: hsl(220, 14.3%, 95.9%);
  --accent-foreground: hsl(220, 8.9%, 46.1%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(221, 83%, 53%);
  --radius: 0.5rem;
  
  /* Custom colors for the app */
  --success: hsl(142, 76%, 36%);
  --warning: hsl(45, 93%, 47%);
  --error: hsl(0, 84.2%, 60.2%);
  
  /* Priority colors */
  --priority-high: hsl(0, 84.2%, 60.2%);
  --priority-medium: hsl(45, 93%, 47%);
  --priority-low: hsl(142, 76%, 36%);
}

.dark {
  --background: hsl(224, 71.4%, 4.1%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(223, 47%, 11%);
  --muted-foreground: hsl(215, 13.8%, 34.1%);
  --popover: hsl(224, 71.4%, 4.1%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(224, 71.4%, 4.1%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(215, 27.9%, 16.9%);
  --input: hsl(215, 27.9%, 16.9%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(223, 47%, 11%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(223, 47%, 11%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(221, 83%, 53%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer components {
  .priority-high {
    @apply bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800;
  }
  
  .priority-medium {
    @apply bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800;
  }
  
  .priority-low {
    @apply bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800;
  }
  
  .status-todo {
    @apply bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400;
  }
  
  .status-in-progress {
    @apply bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400;
  }
  
  .status-review {
    @apply bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400;
  }
  
  .status-done {
    @apply bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400;
  }
  
  .ai-pulse {
    animation: pulse 2s infinite;
  }
  
  .hover-lift {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .hover-lift:hover {
    @apply -translate-y-1 shadow-lg;
  }
  
  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
