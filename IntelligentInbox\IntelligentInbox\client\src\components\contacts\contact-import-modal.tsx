import { useState, useRef } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Upload, FileText, Users, AlertCircle, CheckCircle, Download, Merge } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface ContactImportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ImportResult {
  total: number;
  imported: number;
  duplicates: number;
  errors: number;
  merged: number;
  preview: Array<{
    name: string;
    email: string;
    phone?: string;
    company?: string;
    position?: string;
    source?: string;
  }>;
  duplicatesFound?: Array<{
    existing: any;
    incoming: any;
    similarity: number;
    suggestedAction: string;
  }>;
  formatDetected?: string;
  processingStats?: {
    formatValidation: string;
    duplicateDetection: string;
    dataValidation: string;
    companyNormalization: string;
  };
}

export default function ContactImportModal({ isOpen, onClose }: ContactImportModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set());
  const [duplicateHandling, setDuplicateHandling] = useState<"skip" | "merge" | "replace">("merge");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      const response = await fetch("/api/contacts/import", {
        method: "POST",
        body: formData,
      });
      return response.json();
    },
    onSuccess: (data) => {
      setImportResult(data);
      setIsProcessing(false);
      setProgress(100);
      queryClient.invalidateQueries({ queryKey: ["/api/contacts"] });
      toast({
        title: "Import Complete",
        description: `Successfully imported ${data.imported} contacts, merged ${data.merged} duplicates`,
      });
    },
    onError: (error) => {
      setIsProcessing(false);
      toast({
        title: "Import Failed",
        description: "Failed to import contacts. Please check the file format.",
        variant: "destructive",
      });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setImportResult(null);
    }
  };

  const handleImport = () => {
    if (!file) return;

    setIsProcessing(true);
    setProgress(0);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("duplicateHandling", duplicateHandling);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 90) {
          clearInterval(progressInterval);
          return prev;
        }
        return prev + 10;
      });
    }, 200);

    uploadMutation.mutate(formData);
  };

  const handleExport = async (format: string) => {
    try {
      const response = await apiRequest("GET", `/api/contacts/export?format=${format}`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `contacts.${format}`;
      a.click();
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "Export Complete",
        description: `Contacts exported as ${format.toUpperCase()} file`,
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export contacts",
        variant: "destructive",
      });
    }
  };

  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'csv':
        return <FileText className="w-6 h-6 text-green-500" />;
      case 'vcf':
        return <Users className="w-6 h-6 text-blue-500" />;
      case 'xlsx':
      case 'xls':
        return <FileText className="w-6 h-6 text-orange-500" />;
      default:
        return <FileText className="w-6 h-6 text-gray-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Contact Management</DialogTitle>
          <DialogDescription>
            Import, export, and manage contacts from various formats including Google Contacts, CSV, and vCard
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="import" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="import">Import</TabsTrigger>
            <TabsTrigger value="export">Export</TabsTrigger>
            <TabsTrigger value="merge">Merge & Fix</TabsTrigger>
          </TabsList>

          {/* Import Tab */}
          <TabsContent value="import" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-5 h-5" />
                  <span>Import Contacts</span>
                </CardTitle>
                <CardDescription>
                  Supports CSV, vCard (.vcf), Excel, and Google Contacts formats
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid w-full items-center gap-2">
                  <Label htmlFor="contact-file">Select file</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      ref={fileInputRef}
                      id="contact-file"
                      type="file"
                      accept=".csv,.vcf,.xlsx,.xls"
                      onChange={handleFileSelect}
                      className="cursor-pointer"
                    />
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      Browse
                    </Button>
                  </div>
                </div>

                {file && (
                  <Card className="p-4">
                    <div className="flex items-center space-x-3">
                      {getFileTypeIcon(file.name)}
                      <div className="flex-1">
                        <p className="font-medium">{file.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {(file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                      <Badge variant="outline">
                        {file.type || 'Unknown format'}
                      </Badge>
                    </div>
                  </Card>
                )}

                <div className="space-y-3">
                  <Label>Duplicate Handling</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="merge"
                        checked={duplicateHandling === "merge"}
                        onCheckedChange={() => setDuplicateHandling("merge")}
                      />
                      <Label htmlFor="merge" className="text-sm">
                        Merge duplicates intelligently (recommended)
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="skip"
                        checked={duplicateHandling === "skip"}
                        onCheckedChange={() => setDuplicateHandling("skip")}
                      />
                      <Label htmlFor="skip" className="text-sm">
                        Skip duplicates
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="replace"
                        checked={duplicateHandling === "replace"}
                        onCheckedChange={() => setDuplicateHandling("replace")}
                      />
                      <Label htmlFor="replace" className="text-sm">
                        Replace existing contacts
                      </Label>
                    </div>
                  </div>
                </div>

                {isProcessing && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Processing contacts...</span>
                      <span className="text-sm font-medium">{progress}%</span>
                    </div>
                    <Progress value={progress} className="w-full" />
                  </div>
                )}

                <Button
                  onClick={handleImport}
                  disabled={!file || isProcessing}
                  className="w-full"
                >
                  {isProcessing ? "Processing..." : "Import Contacts"}
                </Button>
              </CardContent>
            </Card>

            {importResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span>Import Results</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{importResult.total}</div>
                      <div className="text-sm text-muted-foreground">Total Processed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{importResult.imported}</div>
                      <div className="text-sm text-muted-foreground">Imported</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{importResult.merged}</div>
                      <div className="text-sm text-muted-foreground">Merged</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{importResult.errors}</div>
                      <div className="text-sm text-muted-foreground">Errors</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Export Tab */}
          <TabsContent value="export" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Download className="w-5 h-5" />
                  <span>Export Contacts</span>
                </CardTitle>
                <CardDescription>
                  Download your contacts in various formats
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    onClick={() => handleExport("csv")}
                    className="h-20 flex-col space-y-2"
                  >
                    <FileText className="w-8 h-8 text-green-500" />
                    <span>CSV Format</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleExport("vcf")}
                    className="h-20 flex-col space-y-2"
                  >
                    <Users className="w-8 h-8 text-blue-500" />
                    <span>vCard (.vcf)</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleExport("xlsx")}
                    className="h-20 flex-col space-y-2"
                  >
                    <FileText className="w-8 h-8 text-orange-500" />
                    <span>Excel Format</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => handleExport("google")}
                    className="h-20 flex-col space-y-2"
                  >
                    <Users className="w-8 h-8 text-red-500" />
                    <span>Google Contacts</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Merge & Fix Tab */}
          <TabsContent value="merge" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Merge className="w-5 h-5" />
                  <span>Merge & Fix Duplicates</span>
                </CardTitle>
                <CardDescription>
                  Automatically detect and merge duplicate contacts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2 p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <AlertCircle className="w-5 h-5 text-blue-500" />
                  <div className="text-sm">
                    <p className="font-medium">Smart Duplicate Detection</p>
                    <p className="text-muted-foreground">
                      Our AI analyzes names, emails, phone numbers, and addresses to find potential duplicates
                    </p>
                  </div>
                </div>
                
                <Button className="w-full">
                  Scan for Duplicates
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}