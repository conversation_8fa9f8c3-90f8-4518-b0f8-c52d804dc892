import type { Email } from "@shared/schema";
import EmailItem from "./email-item";

interface EmailListProps {
  emails: Email[];
  onEmailClick?: (email: Email) => void;
  selectedEmailId?: number;
}

export default function EmailList({ emails, onEmailClick, selectedEmailId }: EmailListProps) {
  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {emails.map((email) => (
        <EmailItem 
          key={email.id} 
          email={email} 
          onClick={() => onEmailClick?.(email)} 
          isSelected={email.id === selectedEmailId}
        />
      ))}
    </div>
  );
}
