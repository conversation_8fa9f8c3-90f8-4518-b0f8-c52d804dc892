import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RefreshCw, Brain, TrendingUp, AlertTriangle, Lightbulb, ThumbsUp, ThumbsDown, CheckSquare, Calendar, FileText, Users, Clock } from "lucide-react";
import { apiRequest } from "@/lib/api";
import type { AiInsight } from "@shared/schema";

export default function AiInsights() {
  const queryClient = useQueryClient();
  
  const { data: insights, isLoading, refetch } = useQuery<AiInsight[]>({
    queryKey: ["/api/ai-insights"],
    queryFn: () => apiRequest("GET", "/api/ai-insights").then(res => res.json()),
  });

  const { data: suggestions, isLoading: suggestionsLoading } = useQuery({
    queryKey: ["/api/ai/generate-suggestions"],
    queryFn: () => apiRequest("POST", "/api/ai/generate-suggestions", {}).then(res => res.json()),
  });

  const feedbackMutation = useMutation({
    mutationFn: ({ type, feedback }: { type: string; feedback: "approve" | "reject" }) =>
      apiRequest("POST", "/api/ai/feedback", { type, feedback }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ai/generate-suggestions"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-insights"] });
    },
  });

  const handleRefresh = () => {
    refetch();
  };

  const handleFeedback = (type: string, feedback: "approve" | "reject") => {
    feedbackMutation.mutate({ type, feedback });
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "conflict": return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case "follow_up": return <TrendingUp className="w-5 h-5 text-blue-500" />;
      case "meeting_request": return <Brain className="w-5 h-5 text-purple-500" />;
      case "task": return <CheckSquare className="w-5 h-5 text-green-500" />;
      case "schedule": return <Calendar className="w-5 h-5 text-blue-500" />;
      case "priority_change": return <TrendingUp className="w-5 h-5 text-orange-500" />;
      default: return <Lightbulb className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">Smart Insights</h2>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-500 dark:text-gray-400">AI-Powered Analysis</span>
            </div>
          </div>
          
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Insights Section */}
      <div className="flex-1 p-6">
        {/* Learning Notice */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start space-x-3">
            <Brain className="w-5 h-5 text-blue-600 mt-1" />
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-100">AI Learning System</h4>
              <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                Your feedback helps the AI learn and improve. Use the thumbs up/down buttons to approve or reject suggestions. 
                The system adapts based on your preferences to provide more relevant insights over time.
              </p>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-4">Smart Suggestions</h3>
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : insights && insights.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mb-8">
              {insights.map((insight) => (
                <Card key={insight.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getInsightIcon(insight.type)}
                        <CardTitle className="text-sm font-medium">
                          {insight.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </CardTitle>
                      </div>
                      <Badge className={getPriorityColor(insight.priority)}>
                        {insight.priority}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm mb-3">
                      {insight.description}
                    </CardDescription>
                    {insight.suggestions && insight.suggestions.length > 0 && (
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Suggestions:</p>
                        {insight.suggestions.slice(0, 2).map((suggestion, idx) => (
                          <p key={idx} className="text-xs text-gray-600 dark:text-gray-300">
                            • {suggestion}
                          </p>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 mb-8">
              <div className="text-center">
                <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No insights available yet</p>
                <p className="text-sm text-gray-400 dark:text-gray-500">AI analysis will appear here as you use the platform</p>
              </div>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">Relationship Insights</h3>
          {suggestionsLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : suggestions && suggestions.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mb-8">
              {suggestions.map((suggestion: any, index: number) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getInsightIcon(suggestion.type)}
                        <CardTitle className="text-sm font-medium">
                          {suggestion.title}
                        </CardTitle>
                      </div>
                      <Badge className={getPriorityColor(suggestion.priority)}>
                        {suggestion.priority}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm mb-3">
                      {suggestion.description}
                    </CardDescription>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {suggestion.type.replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFeedback(suggestion.type, "approve")}
                          className="p-1 h-auto text-green-600 hover:text-green-700 hover:bg-green-50"
                          disabled={feedbackMutation.isPending}
                        >
                          <ThumbsUp className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFeedback(suggestion.type, "reject")}
                          className="p-1 h-auto text-red-600 hover:text-red-700 hover:bg-red-50"
                          disabled={feedbackMutation.isPending}
                        >
                          <ThumbsDown className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No suggestions available yet</p>
                <p className="text-sm text-gray-400 dark:text-gray-500">Smart suggestions will appear here based on your activity</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}