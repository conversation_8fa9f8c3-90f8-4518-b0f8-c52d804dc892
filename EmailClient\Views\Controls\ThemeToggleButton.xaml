<UserControl x:Class="EmailClient.Views.Controls.ThemeToggleButton"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="32" d:DesignWidth="32">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Button x:Name="ToggleButton"
            Style="{StaticResource ModernIconButtonStyle}"
            Width="32" Height="32"
            ToolTip="{Binding TooltipText}"
            Click="ToggleButton_Click">
        <Grid>
            <!-- Light Mode Icon -->
            <TextBlock x:Name="LightIcon"
                       Text="{StaticResource Icon.LightMode}"
                       Style="{StaticResource ModernIconStyle}"
                       FontSize="16"
                       Visibility="{Binding ShowLightIcon, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <!-- Dark Mode Icon -->
            <TextBlock x:Name="DarkIcon"
                       Text="{StaticResource Icon.DarkMode}"
                       Style="{StaticResource ModernIconStyle}"
                       FontSize="16"
                       Visibility="{Binding ShowDarkIcon, Converter={StaticResource BooleanToVisibilityConverter}}"/>

            <!-- System Mode Icon -->
            <TextBlock x:Name="SystemIcon"
                       Text="{StaticResource Icon.SystemMode}"
                       Style="{StaticResource ModernIconStyle}"
                       FontSize="16"
                       Visibility="{Binding ShowSystemIcon, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </Grid>
    </Button>
</UserControl>
