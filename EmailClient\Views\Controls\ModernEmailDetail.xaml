<UserControl x:Class="EmailClient.Views.Controls.ModernEmailDetail"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:EmailClient.Converters"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="500">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
        <local:InvertedNullToVisibilityConverter x:Key="InvertedNullToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Border Background="{StaticResource SurfaceBrush}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Email Header -->
            <Border Grid.Row="0" 
                    Padding="24,20" 
                    BorderBrush="{StaticResource BorderBrush}" 
                    BorderThickness="0,0,0,1"
                    Visibility="{Binding SelectedEmail, Converter={StaticResource NullToVisibilityConverter}}">
                <StackPanel>
                    <!-- Subject and Actions Row -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0"
                                   Text="{Binding SelectedEmail.Subject}" 
                                   Style="{StaticResource HeadingMediumStyle}"
                                   TextWrapping="Wrap"/>
                        
                        <!-- Quick Actions -->
                        <StackPanel Grid.Column="1" 
                                    Orientation="Horizontal"
                                    VerticalAlignment="Top"
                                    Margin="16,0,0,0">
                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                    ToolTip="Star"
                                    Click="StarButton_Click">
                                <TextBlock Text="{Binding SelectedEmail.StarIcon}" FontSize="16"/>
                            </Button>
                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                    ToolTip="Archive"
                                    Margin="4,0,0,0">
                                <TextBlock Text="📦" FontSize="16"/>
                            </Button>
                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                    ToolTip="Delete"
                                    Command="{Binding DeleteCommand}"
                                    Margin="4,0,0,0">
                                <TextBlock Text="🗑" FontSize="16"/>
                            </Button>
                            <Button Style="{StaticResource ModernIconButtonStyle}"
                                    ToolTip="More"
                                    Margin="4,0,0,0">
                                <TextBlock Text="⋯" FontSize="16"/>
                            </Button>
                        </StackPanel>
                    </Grid>
                    
                    <!-- Sender Info -->
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <Border Grid.Column="0" 
                                Width="48" Height="48" 
                                Background="{StaticResource BorderBrush}" 
                                CornerRadius="24"
                                Margin="0,0,16,0">
                            <TextBlock Text="{Binding SelectedEmail.SenderInitial}" 
                                       Style="{StaticResource HeadingSmallStyle}"
                                       HorizontalAlignment="Center" 
                                       VerticalAlignment="Center"/>
                        </Border>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="{Binding SelectedEmail.FromDisplay}" 
                                       Style="{StaticResource BodyTextStyle}"
                                       FontWeight="Medium"/>
                            <TextBlock Text="{Binding SelectedEmail.DateDisplay}" 
                                       Style="{StaticResource CaptionTextStyle}"/>
                        </StackPanel>
                        
                        <!-- Read Status Indicator -->
                        <Border Grid.Column="2" 
                                Background="{StaticResource UnreadBrush}" 
                                CornerRadius="12" 
                                Padding="8,4"
                                VerticalAlignment="Center"
                                Visibility="{Binding SelectedEmail.IsRead, Converter={StaticResource InvertedBooleanToVisibilityConverter}}">
                            <TextBlock Text="UNREAD" 
                                       Style="{StaticResource CaptionTextStyle}"
                                       FontWeight="SemiBold"
                                       Foreground="{StaticResource PrimaryBrush}"/>
                        </Border>
                    </Grid>
                    
                    <!-- Attachments Info -->
                    <Border Background="{StaticResource HoverBrush}"
                            CornerRadius="6"
                            Padding="12,8"
                            Margin="0,0,0,16"
                            Visibility="{Binding SelectedEmail.HasAttachments, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📎" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="This email contains attachments" 
                                       Style="{StaticResource CaptionTextStyle}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Action Buttons -->
                    <StackPanel Orientation="Horizontal">
                        <Button Content="Reply" 
                                Style="{StaticResource ModernButtonStyle}"
                                Command="{Binding ReplyCommand}" 
                                Margin="0,0,8,0">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="6"
                                            Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal" 
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                            <TextBlock Text="↩" FontSize="14" Margin="0,0,8,0"/>
                                            <ContentPresenter/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{StaticResource PrimaryHoverBrush}"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="#1D4ED8"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                        
                        <Button Content="Reply All" 
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Margin="0,0,8,0">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="6"
                                            Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal" 
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                            <TextBlock Text="↩↩" FontSize="12" Margin="0,0,8,0"/>
                                            <ContentPresenter/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                        
                        <Button Content="Forward" 
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Command="{Binding ForwardCommand}" 
                                Margin="0,0,8,0">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="6"
                                            Padding="{TemplateBinding Padding}">
                                        <StackPanel Orientation="Horizontal" 
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center">
                                            <TextBlock Text="→" FontSize="14" Margin="0,0,8,0"/>
                                            <ContentPresenter/>
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter Property="Background" Value="{StaticResource BorderBrush}"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                        
                        <Button Content="{Binding MarkReadButtonText}" 
                                Style="{StaticResource ModernSecondaryButtonStyle}"
                                Command="{Binding MarkReadCommand}"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- Email Body -->
            <Grid Grid.Row="1">
                <WebBrowser x:Name="EmailBodyBrowser"
                            Visibility="{Binding SelectedEmail, Converter={StaticResource NullToVisibilityConverter}}"
                            Margin="24,20"/>

                <!-- No Selection Message -->
                <StackPanel HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Visibility="{Binding SelectedEmail, Converter={StaticResource InvertedNullToVisibilityConverter}}">
                    <TextBlock Text="📧" 
                               FontSize="48" 
                               HorizontalAlignment="Center"
                               Margin="0,0,0,16"
                               Foreground="{StaticResource TextMutedBrush}"/>
                    <TextBlock Text="Select an email to view its content"
                               Style="{StaticResource BodyTextStyle}"
                               Foreground="{StaticResource TextMutedBrush}"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="Choose an email from the list to read it here"
                               Style="{StaticResource CaptionTextStyle}"
                               HorizontalAlignment="Center"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</UserControl>
