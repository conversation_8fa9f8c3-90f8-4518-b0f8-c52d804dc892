import { pgTable, text, serial, integer, boolean, timestamp, jsonb, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  defaultLocation: text("default_location"), // e.g., "123 Main St, City, State"
  workingHours: json("working_hours").$type<{
    start: string; // e.g., "09:00"
    end: string;   // e.g., "17:00"
    timezone: string; // e.g., "America/New_York"
    workDays: number[]; // e.g., [1,2,3,4,5] for Mon-Fri
  }>(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const contacts = pgTable("contacts", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  phone: text("phone"),
  company: text("company"),
  position: text("position"),
  notes: text("notes"),
  avatar: text("avatar"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const emails = pgTable("emails", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  inboxId: integer("inbox_id").references(() => inboxes.id),
  contactId: integer("contact_id").references(() => contacts.id),
  subject: text("subject").notNull(),
  body: text("body").notNull(),
  sender: text("sender").notNull(),
  recipients: text("recipients").array().notNull(),
  priority: text("priority").notNull().default("medium"), // low, medium, high
  isRead: boolean("is_read").default(false),
  isStarred: boolean("is_starred").default(false),
  isImportant: boolean("is_important").default(false),
  isArchived: boolean("is_archived").default(false),
  isDeleted: boolean("is_deleted").default(false),
  attachments: jsonb("attachments").default([]),
  aiSummary: text("ai_summary"),
  relatedItems: jsonb("related_items").default([]), // references to tasks, calendar, etc
  automationTriggered: jsonb("automation_triggered").default([]), // track which automations have been applied
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const tasks = pgTable("tasks", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  description: text("description"),
  priority: text("priority").notNull().default("medium"), // low, medium, high
  status: text("status").notNull().default("todo"), // todo, in_progress, review, done
  dueDate: timestamp("due_date"),
  completed: boolean("completed").default(false),
  completedAt: timestamp("completed_at"),
  relatedEmails: integer("related_emails").array().default([]),
  relatedContacts: integer("related_contacts").array().default([]),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const calendarEvents = pgTable("calendar_events", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  description: text("description"),
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  location: text("location"),
  travelTimeFrom: integer("travel_time_from"), // minutes to travel to this event
  travelTimeTo: integer("travel_time_to"), // minutes to travel from this event
  attendees: text("attendees").array().default([]),
  relatedEmails: integer("related_emails").array().default([]),
  relatedContacts: integer("related_contacts").array().default([]),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  type: text("type").notNull(), // pdf, doc, xlsx, etc
  size: integer("size").notNull(),
  url: text("url").notNull(),
  relatedEmails: integer("related_emails").array().default([]),
  relatedContacts: integer("related_contacts").array().default([]),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// AI Automations for email management
export const aiAutomations = pgTable("ai_automations", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  type: text("type").notNull(), // smart_action, conditional_forward, meeting_scheduler, smart_reply, content_processor, custom
  aiInstructions: text("ai_instructions").notNull(), // Natural language instructions for AI to interpret
  isActive: boolean("is_active").default(true),
  conditions: jsonb("conditions").notNull(), // flexible criteria for automation triggers
  actions: jsonb("actions").notNull(), // configurable actions to perform
  triggerCount: integer("trigger_count").default(0),
  lastTriggered: timestamp("last_triggered"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Inbox management for multiple email accounts
export const inboxes = pgTable("inboxes", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  provider: text("provider").notNull(), // gmail, outlook, yahoo, imap, exchange
  isDefault: boolean("is_default").default(false),
  isActive: boolean("is_active").default(true),
  settings: jsonb("settings").default({}), // provider-specific settings, credentials
  syncStatus: text("sync_status").default("idle"), // idle, syncing, error
  lastSyncAt: timestamp("last_sync_at"),
  errorMessage: text("error_message"),
  unreadCount: integer("unread_count").default(0),
  totalCount: integer("total_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const aiInsights = pgTable("ai_insights", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  type: text("type").notNull(), // suggestion, conflict, relationship
  title: text("title").notNull(),
  description: text("description").notNull(),
  actionUrl: text("action_url"),
  priority: text("priority").notNull().default("medium"),
  isRead: boolean("is_read").default(false),
  relatedData: jsonb("related_data").default({}),
  createdAt: timestamp("created_at").defaultNow(),
});

export const emailTemplates = pgTable("email_templates", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  subject: text("subject").notNull(),
  body: text("body").notNull(),
  category: text("category").notNull(), // "meeting", "followup", "outofoffice", "custom"
  intent: text("intent"), // detected intent this template responds to
  variables: text("variables").array().default([]), // placeholder variables like {{name}}, {{date}}
  isActive: boolean("is_active").default(true),
  usageCount: integer("usage_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const emailFolders = pgTable("email_folders", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  color: text("color").default("#3B82F6"), // hex color
  rules: jsonb("rules").$type<{
    conditions: Array<{
      field: string; // "sender", "subject", "body", "intent", "priority"
      operator: string; // "contains", "equals", "startsWith", "endsWith"
      value: string;
    }>;
    logic: "AND" | "OR";
  }>(),
  isSystem: boolean("is_system").default(false), // for built-in folders
  emailCount: integer("email_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
});

export const emailWorkflows = pgTable("email_workflows", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  name: text("name").notNull(),
  description: text("description"),
  trigger: jsonb("trigger").$type<{
    type: "intent" | "sender" | "subject" | "priority" | "schedule";
    conditions: Array<{
      field: string;
      operator: string;
      value: string;
    }>;
  }>(),
  actions: jsonb("actions").$type<Array<{
    type: "reply" | "forward" | "move_folder" | "set_priority" | "create_task" | "schedule_meeting" | "process_attachments";
    config: Record<string, any>;
  }>>(),
  isActive: boolean("is_active").default(true),
  executionCount: integer("execution_count").default(0),
  lastExecuted: timestamp("last_executed"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  contacts: many(contacts),
  emails: many(emails),
  tasks: many(tasks),
  calendarEvents: many(calendarEvents),
  documents: many(documents),
  aiInsights: many(aiInsights),
  aiAutomations: many(aiAutomations),
  inboxes: many(inboxes),
  emailTemplates: many(emailTemplates),
  emailFolders: many(emailFolders),
  emailWorkflows: many(emailWorkflows),
}));

export const emailTemplatesRelations = relations(emailTemplates, ({ one }) => ({
  user: one(users, { fields: [emailTemplates.userId], references: [users.id] }),
}));

export const emailFoldersRelations = relations(emailFolders, ({ one }) => ({
  user: one(users, { fields: [emailFolders.userId], references: [users.id] }),
}));

export const emailWorkflowsRelations = relations(emailWorkflows, ({ one }) => ({
  user: one(users, { fields: [emailWorkflows.userId], references: [users.id] }),
}));

export const contactsRelations = relations(contacts, ({ one, many }) => ({
  user: one(users, { fields: [contacts.userId], references: [users.id] }),
  emails: many(emails),
}));

export const emailsRelations = relations(emails, ({ one }) => ({
  user: one(users, { fields: [emails.userId], references: [users.id] }),
  contact: one(contacts, { fields: [emails.contactId], references: [contacts.id] }),
  inbox: one(inboxes, { fields: [emails.inboxId], references: [inboxes.id] }),
}));

export const inboxesRelations = relations(inboxes, ({ one, many }) => ({
  user: one(users, { fields: [inboxes.userId], references: [users.id] }),
  emails: many(emails),
}));

export const aiAutomationsRelations = relations(aiAutomations, ({ one }) => ({
  user: one(users, { fields: [aiAutomations.userId], references: [users.id] }),
}));

export const tasksRelations = relations(tasks, ({ one }) => ({
  user: one(users, { fields: [tasks.userId], references: [users.id] }),
}));

export const calendarEventsRelations = relations(calendarEvents, ({ one }) => ({
  user: one(users, { fields: [calendarEvents.userId], references: [users.id] }),
}));

export const documentsRelations = relations(documents, ({ one }) => ({
  user: one(users, { fields: [documents.userId], references: [users.id] }),
}));

export const aiInsightsRelations = relations(aiInsights, ({ one }) => ({
  user: one(users, { fields: [aiInsights.userId], references: [users.id] }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  email: true,
  password: true,
  defaultLocation: true,
  workingHours: true,
});

export const insertContactSchema = createInsertSchema(contacts).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertEmailSchema = createInsertSchema(emails).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertTaskSchema = createInsertSchema(tasks).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  completedAt: true,
});

export const insertCalendarEventSchema = createInsertSchema(calendarEvents).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertDocumentSchema = createInsertSchema(documents).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAiInsightSchema = createInsertSchema(aiInsights).omit({
  id: true,
  createdAt: true,
});

export const insertAiAutomationSchema = createInsertSchema(aiAutomations).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  triggerCount: true,
  lastTriggered: true,
});

export const insertInboxSchema = createInsertSchema(inboxes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastSync: true,
});

export const insertEmailTemplateSchema = createInsertSchema(emailTemplates).omit({
  id: true,
  usageCount: true,
  createdAt: true,
  updatedAt: true,
});

export const insertEmailFolderSchema = createInsertSchema(emailFolders).omit({
  id: true,
  emailCount: true,
  createdAt: true,
});

export const insertEmailWorkflowSchema = createInsertSchema(emailWorkflows).omit({
  id: true,
  executionCount: true,
  lastExecuted: true,
  createdAt: true,
  updatedAt: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;

export type Contact = typeof contacts.$inferSelect;
export type InsertContact = z.infer<typeof insertContactSchema>;

export type Email = typeof emails.$inferSelect;
export type InsertEmail = z.infer<typeof insertEmailSchema>;

export type Task = typeof tasks.$inferSelect;
export type InsertTask = z.infer<typeof insertTaskSchema>;

export type Inbox = typeof inboxes.$inferSelect;
export type InsertInbox = z.infer<typeof insertInboxSchema>;

export type AiAutomation = typeof aiAutomations.$inferSelect;
export type InsertAiAutomation = z.infer<typeof insertAiAutomationSchema>;

export type CalendarEvent = typeof calendarEvents.$inferSelect;
export type InsertCalendarEvent = z.infer<typeof insertCalendarEventSchema>;

export type Document = typeof documents.$inferSelect;
export type InsertDocument = z.infer<typeof insertDocumentSchema>;

export type AiInsight = typeof aiInsights.$inferSelect;
export type InsertAiInsight = z.infer<typeof insertAiInsightSchema>;

export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type InsertEmailTemplate = z.infer<typeof insertEmailTemplateSchema>;

export type EmailFolder = typeof emailFolders.$inferSelect;
export type InsertEmailFolder = z.infer<typeof insertEmailFolderSchema>;

export type EmailWorkflow = typeof emailWorkflows.$inferSelect;
export type InsertEmailWorkflow = z.infer<typeof insertEmailWorkflowSchema>;
