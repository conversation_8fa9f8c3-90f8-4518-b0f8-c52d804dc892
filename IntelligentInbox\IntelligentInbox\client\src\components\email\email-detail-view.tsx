import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { 
  Reply, 
  ReplyAll, 
  Forward, 
  Star, 
  Archive, 
  Trash2, 
  MoreHorizontal,
  Paperclip,
  Send,
  X
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { apiRequest } from "@/lib/api";
import PriorityBadge from "@/components/ui/priority-badge";
import AttachmentHandler from "./attachment-handler";
import type { Email } from "@shared/schema";

interface EmailDetailViewProps {
  email: Email;
  onClose: () => void;
  relatedEmails?: Email[];
  isInlineView?: boolean;
}

export default function EmailDetailView({ email, onClose, relatedEmails = [], isInlineView = false }: EmailDetailViewProps) {
  const [isReplying, setIsReplying] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [replyType, setReplyType] = useState<"reply" | "reply-all" | "forward">("reply");
  
  const queryClient = useQueryClient();

  const toggleStarMutation = useMutation({
    mutationFn: ({ id, starred }: { id: number; starred: boolean }) => 
      apiRequest("POST", `/api/emails/${id}/star`, { starred }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
    },
  });

  const sendReplyMutation = useMutation({
    mutationFn: (data: any) => apiRequest("POST", "/api/emails", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
      setIsReplying(false);
      setReplyContent("");
    },
  });

  const handleStarClick = () => {
    toggleStarMutation.mutate({ id: email.id, starred: !email.isStarred });
  };

  const handleSendReply = () => {
    if (!replyContent.trim()) return;

    const replyData = {
      subject: `Re: ${email.subject}`,
      body: replyContent,
      sender: "<EMAIL>", // This would come from user session
      priority: "medium",
      isRead: true,
      isStarred: false,
      parentId: email.id // Link to parent email for conversation threading
    };

    sendReplyMutation.mutate(replyData);
  };

  // Sort conversation emails by date
  const conversationEmails = [email, ...relatedEmails].sort((a, b) => 
    new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  return (
    <div className={isInlineView ? "flex h-full flex-col" : "fixed inset-0 z-50 bg-background"}>
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="border-b bg-white dark:bg-gray-800 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
              <h2 className="text-lg font-semibold">{email.subject}</h2>
              <PriorityBadge priority={email.priority as "low" | "medium" | "high"} />
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleStarClick}
                className={cn(
                  email.isStarred 
                    ? "text-yellow-500 hover:text-yellow-600" 
                    : "text-gray-400 hover:text-gray-600"
                )}
              >
                <Star className={cn("w-4 h-4", email.isStarred && "fill-current")} />
              </Button>
              <Button variant="ghost" size="sm">
                <Archive className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Trash2 className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Email Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-6 space-y-6">
            {conversationEmails.map((conversationEmail, index) => (
              <Card key={conversationEmail.id} className={cn(
                "transition-all",
                conversationEmail.id === email.id && "ring-2 ring-blue-500 ring-opacity-50"
              )}>
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                          {conversationEmail.sender.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {conversationEmail.sender}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {format(new Date(conversationEmail.createdAt), "MMM d, yyyy 'at' h:mm a")}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {conversationEmail.attachments && Array.isArray(conversationEmail.attachments) && 
                       conversationEmail.attachments.length > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <Paperclip className="w-3 h-3 mr-1" />
                          {conversationEmail.attachments.length}
                        </Badge>
                      )}
                      <span className="text-xs text-gray-500">
                        #{index + 1}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <div className="whitespace-pre-wrap text-gray-900 dark:text-gray-100">
                      {conversationEmail.body}
                    </div>
                  </div>
                  
                  {conversationEmail.aiSummary && (
                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-500">
                      <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                        AI Summary
                      </h4>
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        {conversationEmail.aiSummary}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}

            {/* Smart Attachment Handler - Show for the main email */}
            <AttachmentHandler 
              attachments={[
                // Mock attachments for demo - in production this would come from email data
                {
                  id: "1",
                  name: "Invoice_Q4_2024.pdf",
                  type: "application/pdf",
                  size: 145600,
                  url: "/attachments/invoice_q4_2024.pdf"
                },
                {
                  id: "2", 
                  name: "Budget_Report.xlsx",
                  type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                  size: 89234,
                  url: "/attachments/budget_report.xlsx"
                }
              ]}
              emailId={email.id}
              emailSubject={email.subject}
              senderEmail={email.sender}
            />
          </div>
        </div>

        {/* Reply Section */}
        {!isReplying ? (
          <div className="border-t bg-white dark:bg-gray-800 p-4">
            <div className="flex items-center justify-center space-x-4">
              <Button onClick={() => { setReplyType("reply"); setIsReplying(true); }}>
                <Reply className="w-4 h-4 mr-2" />
                Reply
              </Button>
              <Button variant="outline" onClick={() => { setReplyType("reply-all"); setIsReplying(true); }}>
                <ReplyAll className="w-4 h-4 mr-2" />
                Reply All
              </Button>
              <Button variant="outline" onClick={() => { setReplyType("forward"); setIsReplying(true); }}>
                <Forward className="w-4 h-4 mr-2" />
                Forward
              </Button>
            </div>
          </div>
        ) : (
          <div className="border-t bg-white dark:bg-gray-800 p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">
                    {replyType === "reply" ? "Reply to:" : 
                     replyType === "reply-all" ? "Reply to all:" : "Forward to:"}
                  </span>
                  <span>{email.sender}</span>
                </div>
              </div>
              
              <Textarea
                placeholder={`Write your ${replyType}...`}
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                rows={8}
                className="mb-4"
              />
              
              <div className="flex items-center justify-between">
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsReplying(false);
                    setReplyContent("");
                  }}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSendReply}
                  disabled={!replyContent.trim() || sendReplyMutation.isPending}
                >
                  <Send className="w-4 h-4 mr-2" />
                  {sendReplyMutation.isPending ? "Sending..." : "Send"}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}