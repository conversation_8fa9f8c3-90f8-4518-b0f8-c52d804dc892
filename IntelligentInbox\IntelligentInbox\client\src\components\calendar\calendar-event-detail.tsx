import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  X, 
  Edit, 
  Clock, 
  MapPin, 
  Users, 
  Calendar as CalendarIcon,
  Video,
  Phone,
  Mail,
  Trash2
} from "lucide-react";
import { format } from "date-fns";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import type { CalendarEvent } from "@shared/schema";

interface CalendarEventDetailProps {
  event: CalendarEvent;
  onClose: () => void;
  onEdit?: () => void;
}

export default function CalendarEventDetail({ event, onClose, onEdit }: CalendarEventDetailProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const deleteEventMutation = useMutation({
    mutationFn: (id: number) => apiRequest("DELETE", `/api/calendar/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/calendar"] });
      toast({ title: "Event deleted successfully" });
      onClose();
    },
    onError: () => {
      toast({ title: "Failed to delete event", variant: "destructive" });
    },
  });

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this event?")) {
      deleteEventMutation.mutate(event.id);
    }
  };

  const formatEventTime = (start: Date, end: Date) => {
    const startDate = new Date(start);
    const endDate = new Date(end);
    
    if (startDate.toDateString() === endDate.toDateString()) {
      return `${format(startDate, "MMM d, yyyy")} • ${format(startDate, "h:mm a")} - ${format(endDate, "h:mm a")}`;
    } else {
      return `${format(startDate, "MMM d, yyyy h:mm a")} - ${format(endDate, "MMM d, yyyy h:mm a")}`;
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "meeting": return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "call": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "event": return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case "reminder": return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case "meeting": return <Users className="w-4 h-4" />;
      case "call": return <Phone className="w-4 h-4" />;
      case "event": return <CalendarIcon className="w-4 h-4" />;
      case "reminder": return <Clock className="w-4 h-4" />;
      default: return <CalendarIcon className="w-4 h-4" />;
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-background">
      <div className="flex h-full flex-col">
        {/* Header */}
        <div className="border-b bg-white dark:bg-gray-800 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
              <h2 className="text-lg font-semibold">{event.title}</h2>
              <Badge className={getEventTypeColor(event.type)}>
                {getEventIcon(event.type)}
                <span className="ml-1 capitalize">{event.type}</span>
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleDelete}
                disabled={deleteEventMutation.isPending}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Event Details */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-gray-500" />
                  <span>Event Details</span>
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Time and Duration */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">Date & Time</h4>
                  <p className="text-gray-600 dark:text-gray-400">
                    {formatEventTime(event.startTime, event.endTime)}
                  </p>
                </div>

                {/* Description */}
                {event.description && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">Description</h4>
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      <p className="text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                        {event.description}
                      </p>
                    </div>
                  </div>
                )}

                {/* Location */}
                {event.location && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900 dark:text-white flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>Location</span>
                    </h4>
                    <p className="text-gray-600 dark:text-gray-400">
                      {event.location}
                    </p>
                  </div>
                )}

                {/* Attendees */}
                {event.attendees && Array.isArray(event.attendees) && event.attendees.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900 dark:text-white flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>Attendees ({event.attendees.length})</span>
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {event.attendees.map((attendee: any, index: number) => (
                        <Badge key={index} variant="outline">
                          {typeof attendee === "string" ? attendee : attendee.email || attendee.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div className="pt-4 border-t">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Quick Actions</h4>
                  <div className="flex flex-wrap gap-2">
                    {event.type === "meeting" && (
                      <Button variant="outline" size="sm">
                        <Video className="w-4 h-4 mr-2" />
                        Join Video Call
                      </Button>
                    )}
                    {event.type === "call" && (
                      <Button variant="outline" size="sm">
                        <Phone className="w-4 h-4 mr-2" />
                        Start Call
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <Mail className="w-4 h-4 mr-2" />
                      Send Update
                    </Button>
                    <Button variant="outline" size="sm">
                      <Clock className="w-4 h-4 mr-2" />
                      Set Reminder
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}