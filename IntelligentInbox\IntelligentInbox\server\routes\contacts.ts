import { Router } from "express";
import multer from "multer";
import csv from "csv-parser";
import XLSX from "xlsx";
import { storage } from "../storage";

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });

// Contact import/export endpoints
router.post("/import", upload.single("file"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const { duplicateHandling = "merge" } = req.body;
    const fileExtension = req.file.originalname.split('.').pop()?.toLowerCase();
    
    let contacts: any[] = [];
    
    // Parse different file formats
    switch (fileExtension) {
      case 'csv':
        contacts = await parseCSV(req.file.buffer);
        break;
      case 'xlsx':
      case 'xls':
        contacts = await parseExcel(req.file.buffer);
        break;
      case 'vcf':
        contacts = await parseVCard(req.file.buffer);
        break;
      default:
        return res.status(400).json({ error: "Unsupported file format" });
    }

    // Normalize contact data
    const normalizedContacts = contacts.map(normalizeContact);
    
    // Process contacts with duplicate handling
    const result = await processContacts(normalizedContacts, duplicateHandling);
    
    res.json(result);
  } catch (error) {
    console.error("Error importing contacts:", error);
    res.status(500).json({ error: "Failed to import contacts" });
  }
});

router.get("/export", async (req, res) => {
  try {
    const { format = "csv" } = req.query;
    const contacts = await storage.getContacts(1); // Mock user ID
    
    let fileData: Buffer;
    let contentType: string;
    let filename: string;
    
    switch (format) {
      case 'csv':
        fileData = generateCSV(contacts);
        contentType = 'text/csv';
        filename = 'contacts.csv';
        break;
      case 'xlsx':
        fileData = generateExcel(contacts);
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
        filename = 'contacts.xlsx';
        break;
      case 'vcf':
        fileData = generateVCard(contacts);
        contentType = 'text/vcard';
        filename = 'contacts.vcf';
        break;
      case 'google':
        fileData = generateGoogleContacts(contacts);
        contentType = 'text/csv';
        filename = 'google-contacts.csv';
        break;
      default:
        return res.status(400).json({ error: "Unsupported export format" });
    }
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(fileData);
  } catch (error) {
    console.error("Error exporting contacts:", error);
    res.status(500).json({ error: "Failed to export contacts" });
  }
});

router.post("/deduplicate", async (req, res) => {
  try {
    const contacts = await storage.getContacts(1); // Mock user ID
    const duplicates = findDuplicates(contacts);
    
    res.json({
      total: contacts.length,
      duplicates: duplicates.length,
      groups: duplicates
    });
  } catch (error) {
    console.error("Error finding duplicates:", error);
    res.status(500).json({ error: "Failed to find duplicates" });
  }
});

// Helper functions
async function parseCSV(buffer: Buffer): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const results: any[] = [];
    const stream = require('stream');
    const readable = new stream.Readable();
    readable.push(buffer);
    readable.push(null);
    
    readable
      .pipe(csv())
      .on('data', (data: any) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

async function parseExcel(buffer: Buffer): Promise<any[]> {
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  return XLSX.utils.sheet_to_json(worksheet);
}

async function parseVCard(buffer: Buffer): Promise<any[]> {
  const content = buffer.toString('utf-8');
  const vcards = content.split('BEGIN:VCARD').filter(card => card.trim());
  
  return vcards.map(card => {
    const lines = card.split('\n');
    const contact: any = {};
    
    lines.forEach(line => {
      if (line.startsWith('FN:')) contact.name = line.substring(3);
      if (line.startsWith('EMAIL:')) contact.email = line.substring(6);
      if (line.startsWith('TEL:')) contact.phone = line.substring(4);
      if (line.startsWith('ORG:')) contact.company = line.substring(4);
    });
    
    return contact;
  });
}

function normalizeContact(rawContact: any): any {
  // Handle different CSV/Excel column variations
  const name = rawContact['Name'] || rawContact['Full Name'] || rawContact['name'] || 
               rawContact['first_name'] + ' ' + rawContact['last_name'] || '';
  
  const email = rawContact['Email'] || rawContact['email'] || rawContact['Email Address'] || 
                rawContact['Primary Email'] || '';
  
  const phone = rawContact['Phone'] || rawContact['phone'] || rawContact['Phone Number'] || 
                rawContact['Primary Phone'] || rawContact['Mobile'] || '';
  
  const company = rawContact['Company'] || rawContact['company'] || rawContact['Organization'] || 
                  rawContact['Work'] || '';
  
  const position = rawContact['Title'] || rawContact['Position'] || rawContact['Job Title'] || '';
  
  return {
    name: name.trim(),
    email: email.trim().toLowerCase(),
    phone: phone.trim(),
    company: company.trim(),
    position: position.trim(),
    userId: 1 // Mock user ID
  };
}

async function processContacts(contacts: any[], duplicateHandling: string) {
  let imported = 0;
  let merged = 0;
  let errors = 0;
  const results = [];
  
  for (const contact of contacts) {
    try {
      if (!contact.name || !contact.email) {
        errors++;
        continue;
      }
      
      // Check for existing contact
      const existingContacts = await storage.getContacts(1);
      const existing = existingContacts.find(c => 
        c.email.toLowerCase() === contact.email.toLowerCase() ||
        (c.name.toLowerCase() === contact.name.toLowerCase() && c.company === contact.company)
      );
      
      if (existing) {
        switch (duplicateHandling) {
          case 'skip':
            continue;
          case 'merge':
            // Merge contact data
            const mergedData = {
              ...existing,
              name: contact.name || existing.name,
              email: contact.email || existing.email,
              phone: contact.phone || existing.phone,
              company: contact.company || existing.company,
              position: contact.position || existing.position,
            };
            await storage.updateContact(existing.id, mergedData);
            merged++;
            break;
          case 'replace':
            await storage.updateContact(existing.id, contact);
            imported++;
            break;
        }
      } else {
        await storage.createContact(contact);
        imported++;
      }
      
      results.push(contact);
    } catch (error) {
      errors++;
    }
  }
  
  return {
    total: contacts.length,
    imported,
    merged,
    errors,
    duplicates: contacts.length - imported - merged - errors,
    preview: results.slice(0, 5)
  };
}

function findDuplicates(contacts: any[]): any[] {
  const groups: any[] = [];
  const processed = new Set();
  
  for (let i = 0; i < contacts.length; i++) {
    if (processed.has(i)) continue;
    
    const duplicates = [contacts[i]];
    const contact = contacts[i];
    
    for (let j = i + 1; j < contacts.length; j++) {
      if (processed.has(j)) continue;
      
      const other = contacts[j];
      
      // Check for duplicates based on email, name similarity, or phone
      if (
        contact.email === other.email ||
        (calculateSimilarity(contact.name, other.name) > 0.8 && contact.company === other.company) ||
        (contact.phone && contact.phone === other.phone)
      ) {
        duplicates.push(other);
        processed.add(j);
      }
    }
    
    if (duplicates.length > 1) {
      groups.push(duplicates);
    }
    
    processed.add(i);
  }
  
  return groups;
}

function calculateSimilarity(str1: string, str2: string): number {
  const a = str1.toLowerCase();
  const b = str2.toLowerCase();
  
  if (a === b) return 1;
  
  const distance = levenshteinDistance(a, b);
  const maxLength = Math.max(a.length, b.length);
  
  return 1 - distance / maxLength;
}

function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
}

function generateCSV(contacts: any[]): Buffer {
  const headers = ['Name', 'Email', 'Phone', 'Company', 'Position'];
  const rows = contacts.map(contact => [
    contact.name,
    contact.email,
    contact.phone || '',
    contact.company || '',
    contact.position || ''
  ]);
  
  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');
    
  return Buffer.from(csvContent, 'utf-8');
}

function generateExcel(contacts: any[]): Buffer {
  const worksheet = XLSX.utils.json_to_sheet(contacts.map(contact => ({
    Name: contact.name,
    Email: contact.email,
    Phone: contact.phone || '',
    Company: contact.company || '',
    Position: contact.position || ''
  })));
  
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Contacts');
  
  return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
}

function generateVCard(contacts: any[]): Buffer {
  const vcards = contacts.map(contact => {
    return [
      'BEGIN:VCARD',
      'VERSION:3.0',
      `FN:${contact.name}`,
      `EMAIL:${contact.email}`,
      contact.phone ? `TEL:${contact.phone}` : '',
      contact.company ? `ORG:${contact.company}` : '',
      contact.position ? `TITLE:${contact.position}` : '',
      'END:VCARD'
    ].filter(line => line).join('\n');
  }).join('\n\n');
  
  return Buffer.from(vcards, 'utf-8');
}

function generateGoogleContacts(contacts: any[]): Buffer {
  // Google Contacts CSV format
  const headers = [
    'Name', 'Given Name', 'Family Name', 'E-mail 1 - Value', 
    'Phone 1 - Value', 'Organization 1 - Name', 'Organization 1 - Title'
  ];
  
  const rows = contacts.map(contact => {
    const nameParts = contact.name.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';
    
    return [
      contact.name,
      firstName,
      lastName,
      contact.email,
      contact.phone || '',
      contact.company || '',
      contact.position || ''
    ];
  });
  
  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n');
    
  return Buffer.from(csvContent, 'utf-8');
}

export default router;