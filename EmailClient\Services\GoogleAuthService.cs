using Google.Apis.Auth.OAuth2;
using Google.Apis.Auth.OAuth2.Flows;
using Google.Apis.Auth.OAuth2.Responses;
using Google.Apis.Auth.OAuth2.Requests;
using Google.Apis.Oauth2.v2;
using Google.Apis.Oauth2.v2.Data;
using Google.Apis.PeopleService.v1;
using PeopleServiceV1 = Google.Apis.PeopleService.v1;
using Google.Apis.Services;
using Google.Apis.Util.Store;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using EmailClient.Data;
using EmailClient.Models;
using EmailClient.Models;
using System.Text.Json;
using System.Net;


namespace EmailClient.Services;

public class GoogleAuthService : IGoogleAuthService
{
    private readonly EmailDbContext _context;
    private readonly ILogger<GoogleAuthService> _logger;
    private UserCredential? _credential;
    private GoogleApiSettings? _settings;

    public GoogleAuthService(EmailDbContext context, ILogger<GoogleAuthService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<bool> AuthenticateAsync(string clientId, string clientSecret, string[] scopes)
    {
        try
        {
            _logger.LogInformation("Starting Google authentication with Client ID: {ClientId}", clientId);

            if (string.IsNullOrWhiteSpace(clientId))
            {
                _logger.LogError("Client ID is empty");
                return false;
            }

            if (string.IsNullOrWhiteSpace(clientSecret))
            {
                _logger.LogError("Client Secret is empty");
                return false;
            }

            var clientSecrets = new ClientSecrets
            {
                ClientId = clientId,
                ClientSecret = clientSecret
            };

            // Use a custom data store to save tokens to our database
            var dataStore = new DatabaseTokenStore(_context);

            // Create authorization code flow for desktop application
            var flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
            {
                ClientSecrets = clientSecrets,
                Scopes = scopes,
                DataStore = dataStore
            });

            _logger.LogInformation("Starting OAuth flow with scopes: {Scopes}", string.Join(", ", scopes));

            // Use LocalServerCodeReceiver with fixed port range to avoid changing ports
            var codeReceiver = CreateFixedPortCodeReceiver();
            _logger.LogInformation("OAuth redirect URI will be: {RedirectUri}", codeReceiver.RedirectUri);

            try
            {
                _logger.LogInformation("Starting OAuth authorization flow...");
                _credential = await new AuthorizationCodeInstalledApp(flow, codeReceiver)
                    .AuthorizeAsync("user", CancellationToken.None);

                if (_credential != null)
                {
                    _logger.LogInformation("OAuth flow completed successfully");
                    _logger.LogInformation("Access token received: {HasToken}", !string.IsNullOrEmpty(_credential.Token?.AccessToken));
                    _logger.LogInformation("Refresh token received: {HasRefreshToken}", !string.IsNullOrEmpty(_credential.Token?.RefreshToken));
                    _logger.LogInformation("Token expires at: {ExpiresAt}", _credential.Token?.IssuedUtc.AddSeconds(_credential.Token?.ExpiresInSeconds ?? 0));

                    // Save or update settings
                    await SaveSettingsAsync(clientId, clientSecret, scopes, _credential);

                    _logger.LogInformation("Google authentication successful");
                    return true;
                }

                _logger.LogWarning("OAuth flow completed but credential is null");
                return false;
            }
            catch (Google.Apis.Auth.OAuth2.Responses.TokenResponseException tokenEx)
            {
                _logger.LogError(tokenEx, "OAuth token error: {Error} - {Description}", tokenEx.Error?.Error, tokenEx.Error?.ErrorDescription);
                throw new InvalidOperationException($"OAuth authentication failed: {tokenEx.Error?.Error} - {tokenEx.Error?.ErrorDescription}", tokenEx);
            }
            catch (Exception authEx)
            {
                _logger.LogError(authEx, "OAuth authorization failed");
                throw new InvalidOperationException($"OAuth authentication failed: {authEx.Message}", authEx);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to authenticate with Google: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        try
        {
            var settings = await GetSettingsAsync();
            if (settings == null)
            {
                _logger.LogWarning("No Google API settings found");
                return false;
            }

            if (!settings.IsAuthenticated)
            {
                _logger.LogWarning("Google API settings indicate not authenticated (IsAuthenticated = false)");
                return false;
            }

            if (string.IsNullOrEmpty(settings.AccessToken))
            {
                _logger.LogWarning("No access token found in settings");
                return false;
            }

            if (_credential == null)
            {
                _logger.LogInformation("Loading credential from settings");
                await LoadCredentialFromSettingsAsync();
            }

            if (_credential == null)
            {
                _logger.LogWarning("Failed to load credential from settings");
                return false;
            }

            if (_credential.Token?.IsStale == true)
            {
                _logger.LogInformation("Token is stale, attempting refresh");
                return await RefreshTokenAsync();
            }

            _logger.LogInformation("Authentication check passed");
            return _credential != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking authentication status");
            return false;
        }
    }

    public async Task<UserCredential?> GetCredentialAsync()
    {
        if (await IsAuthenticatedAsync())
        {
            return _credential;
        }
        return null;
    }

    public async Task<bool> RefreshTokenAsync()
    {
        try
        {
            if (_credential?.Token != null)
            {
                var success = await _credential.RefreshTokenAsync(CancellationToken.None);
                if (success)
                {
                    // Update the stored token
                    await UpdateStoredTokenAsync(_credential.Token);
                    _logger.LogInformation("Google token refreshed successfully");
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to refresh Google token");
            return false;
        }
    }

    public async Task RevokeAuthenticationAsync()
    {
        try
        {
            if (_credential != null)
            {
                await _credential.RevokeTokenAsync(CancellationToken.None);
            }

            // Clear stored settings
            var settings = await GetSettingsAsync();
            if (settings != null)
            {
                settings.IsAuthenticated = false;
                settings.AccessToken = string.Empty;
                settings.RefreshToken = string.Empty;
                settings.TokenExpiresAt = null;
                settings.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
            }

            _credential = null;
            _settings = null;

            _logger.LogInformation("Google authentication revoked");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking Google authentication");
        }
    }

    public async Task<GoogleUserInfo?> GetUserInfoAsync()
    {
        try
        {
            var credential = await GetCredentialAsync();
            if (credential == null)
            {
                return null;
            }

            var service = new PeopleServiceV1.PeopleService(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "EmailClient"
            });

            var request = service.People.Get("people/me");
            request.PersonFields = "names,emailAddresses,photos";

            var person = await request.ExecuteAsync();

            return new GoogleUserInfo
            {
                Id = person.ResourceName ?? string.Empty,
                Email = person.EmailAddresses?.FirstOrDefault()?.Value ?? string.Empty,
                Name = person.Names?.FirstOrDefault()?.DisplayName ?? string.Empty,
                GivenName = person.Names?.FirstOrDefault()?.GivenName ?? string.Empty,
                FamilyName = person.Names?.FirstOrDefault()?.FamilyName ?? string.Empty,
                Picture = person.Photos?.FirstOrDefault()?.Url ?? string.Empty,
                VerifiedEmail = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get Google user info");
            return null;
        }
    }

    public async Task<GoogleApiSettings?> GetSettingsAsync()
    {
        if (_settings == null)
        {
            try
            {
                // Ensure the table exists before querying
                await EnsureGoogleApiSettingsTableExistsAsync();
                _settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Google API settings");
                return null;
            }
        }
        return _settings;
    }

    public async Task UpdateSettingsAsync(GoogleApiSettings settings)
    {
        try
        {
            // Ensure the table exists before querying
            await EnsureGoogleApiSettingsTableExistsAsync();

            var existing = await _context.GoogleApiSettings.FirstOrDefaultAsync();
            if (existing != null)
            {
                existing.ClientId = settings.ClientId;
                existing.ClientSecret = settings.ClientSecret;
                existing.ContactsSyncEnabled = settings.ContactsSyncEnabled;
                existing.CalendarSyncEnabled = settings.CalendarSyncEnabled;
                existing.ContactsSyncIntervalMinutes = settings.ContactsSyncIntervalMinutes;
                existing.CalendarSyncIntervalMinutes = settings.CalendarSyncIntervalMinutes;
                existing.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                _context.GoogleApiSettings.Add(settings);
            }

            await _context.SaveChangesAsync();
            _settings = existing ?? settings;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save Google settings");
        }
    }

    public async Task<GoogleUserProfile?> GetUserProfileAsync()
    {
        try
        {
            var credential = await GetCredentialAsync();
            if (credential == null)
                return null;

            var service = new Oauth2Service(new BaseClientService.Initializer()
            {
                HttpClientInitializer = credential,
                ApplicationName = "Email Client"
            });

            var userInfo = await service.Userinfo.Get().ExecuteAsync();
            
            return new GoogleUserProfile
            {
                Id = userInfo.Id ?? string.Empty,
                Email = userInfo.Email ?? string.Empty,
                Name = userInfo.Name ?? string.Empty,
                GivenName = userInfo.GivenName ?? string.Empty,
                FamilyName = userInfo.FamilyName ?? string.Empty,
                Picture = userInfo.Picture ?? string.Empty,
                VerifiedEmail = userInfo.VerifiedEmail ?? false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get user profile");
            return null;
        }
    }

    private async Task SaveSettingsAsync(string clientId, string clientSecret, string[] scopes, UserCredential credential)
    {
        try
        {
            _logger.LogInformation("Saving Google API settings...");

            if (credential?.Token == null)
            {
                _logger.LogError("Cannot save settings: credential or token is null");
                throw new ArgumentNullException(nameof(credential), "Credential or token cannot be null");
            }

            var settings = await GetSettingsAsync() ?? new GoogleApiSettings();

            settings.ClientId = clientId;
            settings.ClientSecret = clientSecret; // Should be encrypted in production
            settings.AccessToken = credential.Token.AccessToken ?? string.Empty; // Should be encrypted in production
            settings.RefreshToken = credential.Token.RefreshToken ?? string.Empty; // Should be encrypted in production
            settings.TokenExpiresAt = credential.Token.IssuedUtc.AddSeconds(credential.Token.ExpiresInSeconds ?? 3600);
            settings.Scopes = JsonSerializer.Serialize(scopes);
            settings.IsAuthenticated = true;
            settings.UpdatedAt = DateTime.UtcNow;

            if (settings.Id == 0)
            {
                _logger.LogInformation("Creating new Google API settings record");
                _context.GoogleApiSettings.Add(settings);
            }
            else
            {
                _logger.LogInformation("Updating existing Google API settings record with ID: {Id}", settings.Id);
            }

            await _context.SaveChangesAsync();
            _settings = settings;

            _logger.LogInformation("Google API settings saved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save Google API settings");
            throw;
        }
    }

    private async Task LoadCredentialFromSettingsAsync()
    {
        var settings = await GetSettingsAsync();
        if (settings == null || !settings.IsAuthenticated)
            return;

        try
        {
            var token = new TokenResponse
            {
                AccessToken = settings.AccessToken,
                RefreshToken = settings.RefreshToken,
                ExpiresInSeconds = settings.TokenExpiresAt.HasValue 
                    ? (long)(settings.TokenExpiresAt.Value - DateTime.UtcNow).TotalSeconds 
                    : 3600,
                IssuedUtc = settings.TokenExpiresAt?.AddSeconds(-3600) ?? DateTime.UtcNow.AddHours(-1)
            };

            var flow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
            {
                ClientSecrets = new ClientSecrets
                {
                    ClientId = settings.ClientId,
                    ClientSecret = settings.ClientSecret
                },
                Scopes = JsonSerializer.Deserialize<string[]>(settings.Scopes) ?? GoogleScopes.DefaultScopes
            });

            _credential = new UserCredential(flow, "user", token);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load credential from settings");
        }
    }

    private async Task UpdateStoredTokenAsync(TokenResponse token)
    {
        var settings = await GetSettingsAsync();
        if (settings != null)
        {
            settings.AccessToken = token.AccessToken;
            settings.RefreshToken = token.RefreshToken ?? settings.RefreshToken;
            settings.TokenExpiresAt = token.IssuedUtc.AddSeconds(token.ExpiresInSeconds ?? 3600);
            settings.UpdatedAt = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
        }
    }

    public Task<GoogleConfigValidationResult> ValidateConfigurationAsync(string clientId, string clientSecret)
    {
        var result = new GoogleConfigValidationResult();

        try
        {
            // Basic validation
            if (string.IsNullOrWhiteSpace(clientId))
            {
                result.Issues.Add("Client ID is empty or missing");
            }
            else if (!clientId.Contains(".googleusercontent.com"))
            {
                result.Issues.Add("Client ID should end with '.googleusercontent.com'");
            }

            if (string.IsNullOrWhiteSpace(clientSecret))
            {
                result.Issues.Add("Client Secret is empty or missing");
            }

            // Try to create a basic OAuth flow to validate credentials
            if (!string.IsNullOrWhiteSpace(clientId) && !string.IsNullOrWhiteSpace(clientSecret))
            {
                try
                {
                    var clientSecrets = new ClientSecrets
                    {
                        ClientId = clientId,
                        ClientSecret = clientSecret
                    };

                    // Test with minimal scope
                    var testFlow = new GoogleAuthorizationCodeFlow(new GoogleAuthorizationCodeFlow.Initializer
                    {
                        ClientSecrets = clientSecrets,
                        Scopes = new[] { "openid", "email", "profile" }
                    });

                    // If we can create the flow without exception, credentials format is likely correct
                    _logger.LogInformation("OAuth flow creation successful - credentials appear valid");
                }
                catch (Exception ex)
                {
                    result.Issues.Add($"Invalid credentials format: {ex.Message}");
                }
            }

            // Add recommendations
            result.Recommendations.Add("Ensure Google Cloud Console project has OAuth 2.0 Client ID configured for 'Web application' (NOT Desktop application)");
            result.Recommendations.Add("Add redirect URIs for ports 8080-8089: 'http://127.0.0.1:8080/authorize/', 'http://127.0.0.1:8081/authorize/', etc. up to 8089");
            result.Recommendations.Add("Enable Google People API and Google Calendar API in Google Cloud Console");
            result.Recommendations.Add("Configure OAuth consent screen with your email as a test user");
            result.Recommendations.Add("Set OAuth consent screen to 'External' and add your email as a test user");
            result.Recommendations.Add("Make sure no firewall or antivirus is blocking ports 8080-8089");

            result.IsValid = result.Issues.Count == 0;

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating Google configuration");
            result.Issues.Add($"Validation error: {ex.Message}");
            result.IsValid = false;
            return Task.FromResult(result);
        }
    }

    public async Task<bool> ForceEnableAuthenticationAsync()
    {
        try
        {
            var settings = await GetSettingsAsync();
            if (settings == null)
            {
                _logger.LogWarning("No Google API settings found to enable authentication");
                return false;
            }

            if (string.IsNullOrEmpty(settings.AccessToken))
            {
                _logger.LogWarning("Cannot enable authentication - no access token found");
                return false;
            }

            settings.IsAuthenticated = true;
            settings.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();

            _logger.LogInformation("Manually enabled Google authentication");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to force enable authentication");
            return false;
        }
    }

    private async Task EnsureGoogleApiSettingsTableExistsAsync()
    {
        try
        {
            // Check if the table exists by trying to query it
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS ""GoogleApiSettings"" (
                    ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_GoogleApiSettings"" PRIMARY KEY AUTOINCREMENT,
                    ""ClientId"" TEXT NOT NULL DEFAULT '',
                    ""ClientSecret"" TEXT NOT NULL DEFAULT '',
                    ""AccessToken"" TEXT NOT NULL DEFAULT '',
                    ""RefreshToken"" TEXT NOT NULL DEFAULT '',
                    ""TokenExpiresAt"" TEXT,
                    ""Scopes"" TEXT NOT NULL DEFAULT '',
                    ""ContactsSyncEnabled"" INTEGER NOT NULL DEFAULT 1,
                    ""CalendarSyncEnabled"" INTEGER NOT NULL DEFAULT 1,
                    ""ContactsSyncIntervalMinutes"" INTEGER NOT NULL DEFAULT 60,
                    ""CalendarSyncIntervalMinutes"" INTEGER NOT NULL DEFAULT 60,
                    ""LastContactsSync"" TEXT,
                    ""LastCalendarSync"" TEXT,
                    ""ContactsSyncToken"" TEXT NOT NULL DEFAULT '',
                    ""CalendarSyncToken"" TEXT NOT NULL DEFAULT '',
                    ""IsAuthenticated"" INTEGER NOT NULL DEFAULT 0,
                    ""CreatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""UpdatedAt"" TEXT NOT NULL DEFAULT (datetime('now'))
                );");

            // Also ensure CalendarEvents table exists
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS ""CalendarEvents"" (
                    ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_CalendarEvents"" PRIMARY KEY AUTOINCREMENT,
                    ""GoogleId"" TEXT NOT NULL DEFAULT '',
                    ""CalendarId"" TEXT NOT NULL DEFAULT '',
                    ""Summary"" TEXT NOT NULL DEFAULT '',
                    ""Description"" TEXT NOT NULL DEFAULT '',
                    ""Location"" TEXT NOT NULL DEFAULT '',
                    ""StartDateTime"" TEXT,
                    ""EndDateTime"" TEXT,
                    ""IsAllDay"" INTEGER NOT NULL DEFAULT 0,
                    ""TimeZone"" TEXT NOT NULL DEFAULT '',
                    ""Status"" TEXT NOT NULL DEFAULT '',
                    ""Visibility"" TEXT NOT NULL DEFAULT '',
                    ""Attendees"" TEXT NOT NULL DEFAULT '',
                    ""Organizer"" TEXT NOT NULL DEFAULT '',
                    ""Creator"" TEXT NOT NULL DEFAULT '',
                    ""RecurrenceRule"" TEXT NOT NULL DEFAULT '',
                    ""RecurringEventId"" TEXT NOT NULL DEFAULT '',
                    ""HtmlLink"" TEXT NOT NULL DEFAULT '',
                    ""HangoutLink"" TEXT NOT NULL DEFAULT '',
                    ""ConferenceData"" TEXT NOT NULL DEFAULT '',
                    ""Reminders"" TEXT NOT NULL DEFAULT '',
                    ""CreatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""UpdatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""LastSyncAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""ETag"" TEXT NOT NULL DEFAULT '',
                    ""IsDeleted"" INTEGER NOT NULL DEFAULT 0
                );");

            // Also ensure Contacts table exists
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS ""Contacts"" (
                    ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_Contacts"" PRIMARY KEY AUTOINCREMENT,
                    ""GoogleId"" TEXT NOT NULL DEFAULT '',
                    ""DisplayName"" TEXT NOT NULL DEFAULT '',
                    ""GivenName"" TEXT NOT NULL DEFAULT '',
                    ""FamilyName"" TEXT NOT NULL DEFAULT '',
                    ""MiddleName"" TEXT NOT NULL DEFAULT '',
                    ""EmailAddresses"" TEXT NOT NULL DEFAULT '',
                    ""PhoneNumbers"" TEXT NOT NULL DEFAULT '',
                    ""Addresses"" TEXT NOT NULL DEFAULT '',
                    ""Organizations"" TEXT NOT NULL DEFAULT '',
                    ""PhotoUrl"" TEXT NOT NULL DEFAULT '',
                    ""Notes"" TEXT NOT NULL DEFAULT '',
                    ""Birthday"" TEXT,
                    ""CreatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""UpdatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""LastSyncAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""ETag"" TEXT NOT NULL DEFAULT '',
                    ""IsDeleted"" INTEGER NOT NULL DEFAULT 0
                );");

            _logger.LogInformation("Google API tables ensured to exist");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to ensure Google API tables exist");
        }
    }

    private ICodeReceiver CreateFixedPortCodeReceiver()
    {
        // Try to use a fixed port range (8080-8089) to minimize the number of redirect URIs needed
        var preferredPorts = new[] { 8080, 8081, 8082, 8083, 8084, 8085, 8086, 8087, 8088, 8089 };

        foreach (var port in preferredPorts)
        {
            try
            {
                // Test if the port is available by creating a temporary listener
                using (var testListener = new HttpListener())
                {
                    testListener.Prefixes.Add($"http://127.0.0.1:{port}/");
                    testListener.Start();
                    testListener.Stop();
                }

                var codeReceiver = new FixedPortCodeReceiver(port, _logger);
                _logger.LogInformation("Using OAuth redirect port: {Port}", port);
                return codeReceiver;
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Port {Port} not available: {Error}", port, ex.Message);
                // Continue to next port
            }
        }

        // Fallback to default behavior if all preferred ports are taken
        _logger.LogWarning("All preferred ports (8080-8089) are in use, falling back to random port");
        return new LocalServerCodeReceiver();
    }
}

// Custom token store that saves tokens to our database
internal class DatabaseTokenStore : IDataStore
{
    private readonly EmailDbContext _context;

    public DatabaseTokenStore(EmailDbContext context)
    {
        _context = context;
    }

    public async Task StoreAsync<T>(string key, T value)
    {
        try
        {
            // Ensure the table exists before querying
            await EnsureGoogleApiSettingsTableExistsAsync();

            var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
            if (settings != null && value is TokenResponse token)
            {
                settings.AccessToken = token.AccessToken;
                settings.RefreshToken = token.RefreshToken ?? settings.RefreshToken;
                settings.TokenExpiresAt = token.IssuedUtc.AddSeconds(token.ExpiresInSeconds ?? 3600);
                settings.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
        }
        catch (Exception)
        {
            // Silently handle errors in token storage
        }
    }

    public async Task DeleteAsync<T>(string key)
    {
        var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
        if (settings != null)
        {
            settings.AccessToken = string.Empty;
            settings.RefreshToken = string.Empty;
            settings.TokenExpiresAt = null;
            settings.IsAuthenticated = false;
            settings.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
        }
    }

    public async Task<T?> GetAsync<T>(string key)
    {
        try
        {
            // Ensure the table exists before querying
            await EnsureGoogleApiSettingsTableExistsAsync();

            var settings = await _context.GoogleApiSettings.FirstOrDefaultAsync();
            if (settings != null && !string.IsNullOrEmpty(settings.AccessToken))
            {
                var token = new TokenResponse
                {
                    AccessToken = settings.AccessToken,
                    RefreshToken = settings.RefreshToken,
                    ExpiresInSeconds = settings.TokenExpiresAt.HasValue
                        ? (long)(settings.TokenExpiresAt.Value - DateTime.UtcNow).TotalSeconds
                        : 3600,
                    IssuedUtc = settings.TokenExpiresAt?.AddSeconds(-3600) ?? DateTime.UtcNow.AddHours(-1)
                };

                return (T)(object)token;
            }

            return default(T);
        }
        catch (Exception)
        {
            return default(T);
        }
    }

    public Task ClearAsync()
    {
        return DeleteAsync<TokenResponse>("user");
    }

    private async Task EnsureGoogleApiSettingsTableExistsAsync()
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE IF NOT EXISTS ""GoogleApiSettings"" (
                    ""Id"" INTEGER NOT NULL CONSTRAINT ""PK_GoogleApiSettings"" PRIMARY KEY AUTOINCREMENT,
                    ""ClientId"" TEXT NOT NULL DEFAULT '',
                    ""ClientSecret"" TEXT NOT NULL DEFAULT '',
                    ""AccessToken"" TEXT NOT NULL DEFAULT '',
                    ""RefreshToken"" TEXT NOT NULL DEFAULT '',
                    ""TokenExpiresAt"" TEXT,
                    ""Scopes"" TEXT NOT NULL DEFAULT '',
                    ""ContactsSyncEnabled"" INTEGER NOT NULL DEFAULT 1,
                    ""CalendarSyncEnabled"" INTEGER NOT NULL DEFAULT 1,
                    ""ContactsSyncIntervalMinutes"" INTEGER NOT NULL DEFAULT 60,
                    ""CalendarSyncIntervalMinutes"" INTEGER NOT NULL DEFAULT 60,
                    ""LastContactsSync"" TEXT,
                    ""LastCalendarSync"" TEXT,
                    ""ContactsSyncToken"" TEXT NOT NULL DEFAULT '',
                    ""CalendarSyncToken"" TEXT NOT NULL DEFAULT '',
                    ""IsAuthenticated"" INTEGER NOT NULL DEFAULT 0,
                    ""CreatedAt"" TEXT NOT NULL DEFAULT (datetime('now')),
                    ""UpdatedAt"" TEXT NOT NULL DEFAULT (datetime('now'))
                );");
        }
        catch (Exception)
        {
            // Silently handle table creation errors
        }
    }
}

// Custom code receiver that uses a fixed port
internal class FixedPortCodeReceiver : ICodeReceiver
{
    private readonly int _port;
    private readonly string _host;
    private readonly ILogger _logger;
    private HttpListener? _httpListener;

    public FixedPortCodeReceiver(int port, ILogger logger, string host = "127.0.0.1")
    {
        _port = port;
        _host = host;
        _logger = logger;
        RedirectUri = $"http://{_host}:{_port}/authorize/";
    }

    public string RedirectUri { get; }

    public async Task<AuthorizationCodeResponseUrl> ReceiveCodeAsync(
        AuthorizationCodeRequestUrl url,
        CancellationToken taskCancellationToken)
    {
        var authorizationUrl = url.Build().ToString();
        _logger.LogInformation("Starting OAuth callback listener on {Host}:{Port}", _host, _port);
        _logger.LogInformation("Authorization URL: {Url}", authorizationUrl);

        // Start the HTTP listener on the fixed port
        _httpListener = new HttpListener();
        _httpListener.Prefixes.Add($"http://{_host}:{_port}/");

        try
        {
            _httpListener.Start();
            _logger.LogInformation("HTTP listener started successfully on port {Port}", _port);

            // Open the browser to the authorization URL
            OpenBrowser(authorizationUrl);

            // Wait for the callback
            using (taskCancellationToken.Register(() => _httpListener?.Stop()))
            {
                _logger.LogInformation("Waiting for OAuth callback...");
                var context = await _httpListener.GetContextAsync();
                var response = context.Response;

                _logger.LogInformation("Received OAuth callback from {RemoteEndPoint}", context.Request.RemoteEndPoint);
                _logger.LogInformation("Request URL: {Url}", context.Request.Url);
                _logger.LogInformation("Query string: {Query}", context.Request.Url?.Query);

                // Send a simple response to the browser
                var responseString = @"
                    <html>
                        <head><title>OAuth Authorization</title></head>
                        <body>
                            <h1>Authorization Complete</h1>
                            <p>You can close this window and return to the application.</p>
                            <script>window.close();</script>
                        </body>
                    </html>";

                var buffer = System.Text.Encoding.UTF8.GetBytes(responseString);
                response.ContentLength64 = buffer.Length;
                response.ContentType = "text/html";
                await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
                response.OutputStream.Close();

                // Parse the query string to get the authorization code
                var requestUrl = context.Request.Url;
                if (requestUrl == null)
                {
                    _logger.LogError("No URL received in OAuth callback");
                    throw new InvalidOperationException("No URL received in OAuth callback");
                }

                _logger.LogInformation("Parsing query string from URL: {Url}", requestUrl);

                // Manually parse the query string
                var queryString = requestUrl.Query?.TrimStart('?') ?? "";
                var queryParams = ParseQueryString(queryString);
                var authCode = queryParams.GetValueOrDefault("code");
                var state = queryParams.GetValueOrDefault("state");
                var error = queryParams.GetValueOrDefault("error");

                _logger.LogInformation("Parsed authorization code: {Code}", authCode);
                _logger.LogInformation("Parsed state: {State}", state);
                _logger.LogInformation("Parsed error: {Error}", error);

                // Create the response URL object with parsed values
                var result = new AuthorizationCodeResponseUrl
                {
                    Code = authCode,
                    State = state,
                    Error = error
                };

                return result;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in OAuth callback receiver");
            throw;
        }
        finally
        {
            try
            {
                _httpListener?.Stop();
                _httpListener?.Close();
                _logger.LogInformation("HTTP listener stopped");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error stopping HTTP listener");
            }
        }
    }

    private void OpenBrowser(string url)
    {
        try
        {
            _logger.LogInformation("Opening browser to: {Url}", url);
            System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
            {
                FileName = url,
                UseShellExecute = true
            });
            _logger.LogInformation("Browser opened successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to open browser. User will need to navigate manually to: {Url}", url);
            // If we can't open the browser, the user will need to navigate manually
            // This is not ideal but prevents the application from crashing
        }
    }

    private static Dictionary<string, string> ParseQueryString(string queryString)
    {
        var result = new Dictionary<string, string>();
        if (string.IsNullOrEmpty(queryString))
            return result;

        var pairs = queryString.Split('&');
        foreach (var pair in pairs)
        {
            var keyValue = pair.Split('=', 2);
            if (keyValue.Length == 2)
            {
                var key = Uri.UnescapeDataString(keyValue[0]);
                var value = Uri.UnescapeDataString(keyValue[1]);
                result[key] = value;
            }
        }
        return result;
    }
}
