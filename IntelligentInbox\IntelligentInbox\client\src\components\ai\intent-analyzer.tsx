import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Brain, Zap, CheckCircle, AlertCircle, Eye } from "lucide-react";
import { apiRequest } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface EmailIntent {
  intent: string;
  confidence: number;
  category: "request" | "information" | "complaint" | "inquiry" | "action" | "notification";
  entities: {
    type: string;
    value: string;
    confidence: number;
  }[];
  urgency: "low" | "medium" | "high";
  requiresResponse: boolean;
  suggestedActions: string[];
}

interface IntentAnalyzerProps {
  emailText: string;
  onIntentDetected: (intent: EmailIntent) => void;
  autoAnalyze?: boolean;
}

export default function IntentAnalyzer({ emailText, onIntentDetected, autoAnalyze = false }: IntentAnalyzerProps) {
  const [intent, setIntent] = useState<EmailIntent | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [sampleText, setSampleText] = useState(emailText);
  const { toast } = useToast();

  useEffect(() => {
    if (autoAnalyze && emailText) {
      analyzeIntent();
    }
  }, [emailText, autoAnalyze]);

  const analyzeIntent = async () => {
    if (!sampleText.trim()) {
      toast({
        title: "No text to analyze",
        description: "Please enter some email text to analyze intent.",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const response = await apiRequest("POST", "/api/ai/analyze-intent", {
        text: sampleText,
        context: "email"
      });
      
      const detectedIntent = await response.json();
      setIntent(detectedIntent);
      onIntentDetected(detectedIntent);
      
      toast({
        title: "Intent analysis complete",
        description: `Detected intent: ${detectedIntent.intent} (${Math.round(detectedIntent.confidence * 100)}% confidence)`,
      });
    } catch (error) {
      toast({
        title: "Analysis failed",
        description: "Could not analyze email intent. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getIntentIcon = (category: string) => {
    switch (category) {
      case "request": return "🙋‍♂️";
      case "information": return "📄";
      case "complaint": return "⚠️";
      case "inquiry": return "❓";
      case "action": return "⚡";
      case "notification": return "🔔";
      default: return "📧";
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Brain className="w-5 h-5 mr-2" />
          Email Intent Recognition
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!autoAnalyze && (
          <div className="space-y-2">
            <Label htmlFor="email-text">Email Text to Analyze:</Label>
            <Textarea
              id="email-text"
              placeholder="Paste email content here to analyze intent..."
              value={sampleText}
              onChange={(e) => setSampleText(e.target.value)}
              rows={4}
            />
            <Button 
              onClick={analyzeIntent}
              disabled={isAnalyzing || !sampleText.trim()}
              className="w-full"
            >
              {isAnalyzing ? (
                <>
                  <Brain className="w-4 h-4 mr-2 animate-pulse" />
                  Analyzing Intent...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Analyze Email Intent
                </>
              )}
            </Button>
          </div>
        )}

        {intent && (
          <div className="space-y-4 pt-4 border-t">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Detected Intent</h3>
              <Badge variant="outline" className={getConfidenceColor(intent.confidence)}>
                {Math.round(intent.confidence * 100)}% confidence
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="text-2xl">{getIntentIcon(intent.category)}</span>
                  <div>
                    <p className="font-medium">{intent.intent}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {intent.category}
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Badge className={getUrgencyColor(intent.urgency)}>
                    {intent.urgency.toUpperCase()} PRIORITY
                  </Badge>
                  {intent.requiresResponse && (
                    <Badge variant="outline">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Response Required
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {intent.entities.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Detected Entities:</h4>
                <div className="flex flex-wrap gap-2">
                  {intent.entities.map((entity, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center space-x-1">
                      <span className="font-medium">{entity.type}:</span>
                      <span>{entity.value}</span>
                      <span className="text-xs opacity-70">
                        ({Math.round(entity.confidence * 100)}%)
                      </span>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {intent.suggestedActions.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Suggested Actions:</h4>
                <div className="space-y-1">
                  {intent.suggestedActions.map((action, index) => (
                    <div key={index} className="flex items-center space-x-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>{action}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!intent && !isAnalyzing && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No intent analysis available yet</p>
            <p className="text-sm">Analyze email text to see intent recognition results</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}