import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { 
  FileText, 
  FileImage, 
  FileSpreadsheet, 
  File, 
  Send, 
  FolderOpen, 
  Archive, 
  Forward,
  Download,
  Zap,
  Brain,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/api";

interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
}

interface AttachmentHandlerProps {
  attachments: Attachment[];
  emailId: number;
  emailSubject: string;
  senderEmail: string;
}

interface SmartRoute {
  id: string;
  name: string;
  description: string;
  destination: string;
  fileTypes: string[];
  confidence: number;
  action: "forward" | "save" | "process" | "archive";
}

const getFileIcon = (type: string) => {
  if (type.includes('image')) return FileImage;
  if (type.includes('spreadsheet') || type.includes('excel')) return FileSpreadsheet;
  if (type.includes('pdf') || type.includes('document')) return FileText;
  return File;
};

const getFileTypeLabel = (type: string) => {
  if (type.includes('pdf')) return 'PDF';
  if (type.includes('image')) return 'Image';
  if (type.includes('excel') || type.includes('spreadsheet')) return 'Spreadsheet';
  if (type.includes('document') || type.includes('word')) return 'Document';
  return 'File';
};

export default function AttachmentHandler({ attachments, emailId, emailSubject, senderEmail }: AttachmentHandlerProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [smartRoutes, setSmartRoutes] = useState<SmartRoute[]>([]);
  const [selectedRoutes, setSelectedRoutes] = useState<{ [key: string]: string }>({});
  const [customDestination, setCustomDestination] = useState("");
  const [autoReply, setAutoReply] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const analyzeAttachments = async () => {
    setIsAnalyzing(true);
    try {
      // Simulate AI analysis of attachments
      const response = await apiRequest("POST", "/api/ai/analyze-attachments", {
        attachments,
        emailSubject,
        senderEmail,
        emailId
      });
      
      const routes = await response.json();
      setSmartRoutes(routes);
      
      // Pre-select highest confidence routes
      const preSelected: { [key: string]: string } = {};
      attachments.forEach(attachment => {
        const bestRoute = routes
          .filter((r: SmartRoute) => r.fileTypes.some(ft => attachment.type.includes(ft)))
          .sort((a: SmartRoute, b: SmartRoute) => b.confidence - a.confidence)[0];
        if (bestRoute) {
          preSelected[attachment.id] = bestRoute.id;
        }
      });
      setSelectedRoutes(preSelected);
      
      toast({
        title: "Smart routing analysis complete",
        description: `Found ${routes.length} potential destinations for your attachments.`,
      });
    } catch (error) {
      toast({
        title: "Analysis failed",
        description: "Could not analyze attachments for smart routing.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const processAttachments = async () => {
    setIsProcessing(true);
    try {
      const processingTasks = attachments.map(attachment => {
        const routeId = selectedRoutes[attachment.id];
        const route = smartRoutes.find(r => r.id === routeId);
        
        return {
          attachmentId: attachment.id,
          attachmentName: attachment.name,
          route: route || { destination: customDestination, action: "forward" },
          autoReply
        };
      });

      await apiRequest("POST", "/api/ai/process-attachments", {
        emailId,
        tasks: processingTasks,
        emailSubject,
        senderEmail
      });

      toast({
        title: "Attachments processed successfully",
        description: `${attachments.length} attachments have been routed to their destinations.`,
      });

    } catch (error) {
      toast({
        title: "Processing failed",
        description: "Could not process attachments.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (attachments.length === 0) {
    return null;
  }

  return (
    <Card className="mt-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Attachments ({attachments.length})
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={analyzeAttachments}
              disabled={isAnalyzing}
            >
              {isAnalyzing ? (
                <>
                  <Brain className="w-4 h-4 mr-2 animate-pulse" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Zap className="w-4 h-4 mr-2" />
                  Smart Route
                </>
              )}
            </Button>
            {smartRoutes.length > 0 && (
              <Dialog>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Send className="w-4 h-4 mr-2" />
                    Process All
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Smart Attachment Processing</DialogTitle>
                    <DialogDescription>
                      Review and confirm the AI-suggested routing for each attachment
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    {attachments.map(attachment => {
                      const FileIcon = getFileIcon(attachment.type);
                      const selectedRoute = smartRoutes.find(r => r.id === selectedRoutes[attachment.id]);
                      
                      return (
                        <div key={attachment.id} className="border rounded-lg p-4">
                          <div className="flex items-start space-x-3">
                            <FileIcon className="w-6 h-6 text-blue-500 mt-1" />
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <div>
                                  <h4 className="font-medium">{attachment.name}</h4>
                                  <div className="flex items-center space-x-2 mt-1">
                                    <Badge variant="outline">{getFileTypeLabel(attachment.type)}</Badge>
                                    <span className="text-xs text-gray-500">
                                      {(attachment.size / 1024).toFixed(1)} KB
                                    </span>
                                  </div>
                                </div>
                                <Button variant="ghost" size="sm">
                                  <Download className="w-4 h-4" />
                                </Button>
                              </div>
                              
                              <div className="space-y-2">
                                <Label className="text-sm font-medium">Destination:</Label>
                                <Select
                                  value={selectedRoutes[attachment.id] || ""}
                                  onValueChange={(value) => 
                                    setSelectedRoutes(prev => ({ ...prev, [attachment.id]: value }))
                                  }
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select destination" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {smartRoutes
                                      .filter(route => route.fileTypes.some(ft => attachment.type.includes(ft)))
                                      .map(route => (
                                        <SelectItem key={route.id} value={route.id}>
                                          <div className="flex items-center justify-between w-full">
                                            <span>{route.name}</span>
                                            <div className="flex items-center space-x-2 ml-2">
                                              <Badge variant="secondary" className="text-xs">
                                                {Math.round(route.confidence * 100)}%
                                              </Badge>
                                              {route.confidence > 0.8 ? (
                                                <CheckCircle className="w-3 h-3 text-green-500" />
                                              ) : (
                                                <AlertCircle className="w-3 h-3 text-yellow-500" />
                                              )}
                                            </div>
                                          </div>
                                        </SelectItem>
                                      ))}
                                    <SelectItem value="custom">Custom Destination</SelectItem>
                                  </SelectContent>
                                </Select>
                                
                                {selectedRoute && (
                                  <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded text-xs">
                                    <strong>Action:</strong> {selectedRoute.action} → {selectedRoute.destination}
                                    <br />
                                    <span className="text-gray-600 dark:text-gray-400">
                                      {selectedRoute.description}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    
                    {Object.values(selectedRoutes).includes("custom") && (
                      <div className="border rounded-lg p-4 bg-yellow-50 dark:bg-yellow-900/20">
                        <Label className="text-sm font-medium mb-2 block">Custom Destination:</Label>
                        <Input
                          placeholder="Enter email address or folder path"
                          value={customDestination}
                          onChange={(e) => setCustomDestination(e.target.value)}
                        />
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={autoReply}
                          onCheckedChange={setAutoReply}
                          id="auto-reply"
                        />
                        <Label htmlFor="auto-reply" className="text-sm">
                          Send confirmation reply to sender
                        </Label>
                      </div>
                      
                      <Button 
                        onClick={processAttachments}
                        disabled={isProcessing || Object.keys(selectedRoutes).length === 0}
                      >
                        {isProcessing ? (
                          <>
                            <Brain className="w-4 h-4 mr-2 animate-pulse" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            Process {attachments.length} Attachments
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {attachments.map(attachment => {
            const FileIcon = getFileIcon(attachment.type);
            return (
              <div key={attachment.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                <FileIcon className="w-8 h-8 text-blue-500" />
                <div className="flex-1 min-w-0">
                  <p className="font-medium truncate">{attachment.name}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="outline" className="text-xs">
                      {getFileTypeLabel(attachment.type)}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {(attachment.size / 1024).toFixed(1)} KB
                    </span>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="w-4 h-4" />
                </Button>
              </div>
            );
          })}
        </div>
        
        {smartRoutes.length === 0 && (
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="flex items-start space-x-2">
              <Brain className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200">
                  Smart Routing Available
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  Click "Smart Route" to let AI analyze and suggest the best destinations for these attachments based on their content and your previous patterns.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}