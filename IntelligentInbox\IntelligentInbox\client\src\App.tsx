import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/dashboard";
import Inbox from "@/pages/inbox";
import Contacts from "@/pages/contacts";
import Tasks from "@/pages/tasks";
import Calendar from "@/pages/calendar";
import Documents from "@/pages/documents";
import AiInsights from "@/pages/ai-insights";
import AiAutomations from "@/pages/ai-automations";
import AttachmentDemo from "@/pages/attachment-demo";
import Settings from "@/pages/settings";
import AiScheduler from "@/pages/ai-scheduler";
import EmailTemplates from "@/pages/email-templates";
import EmailWorkflows from "@/pages/email-workflows";
import MainLayout from "@/components/layout/main-layout";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Dashboard} />
      <Route path="/inbox" component={Inbox} />
      <Route path="/contacts" component={Contacts} />
      <Route path="/tasks" component={Tasks} />
      <Route path="/calendar" component={Calendar} />
      <Route path="/documents" component={Documents} />
      <Route path="/ai-insights" component={AiInsights} />
      <Route path="/ai-automations" component={AiAutomations} />
      <Route path="/email-templates" component={EmailTemplates} />
      <Route path="/email-workflows" component={EmailWorkflows} />
      <Route path="/attachment-demo" component={AttachmentDemo} />
      <Route path="/ai-scheduler" component={AiScheduler} />
      <Route path="/settings" component={Settings} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <MainLayout>
          <Router />
        </MainLayout>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
