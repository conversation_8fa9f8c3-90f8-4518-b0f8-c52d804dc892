using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace EmailClient.Views.Controls;

public partial class ModernSearchBar : UserControl, INotifyPropertyChanged
{
    public static readonly DependencyProperty SearchQueryProperty =
        DependencyProperty.Register("SearchQuery", typeof(string), 
            typeof(ModernSearchBar), new PropertyMetadata(string.Empty, OnSearchQueryChanged));

    public static readonly RoutedEvent SearchRequestedEvent = 
        EventManager.RegisterRoutedEvent("SearchRequested", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernSearchBar));

    public static readonly RoutedEvent SearchClearedEvent = 
        EventManager.RegisterRoutedEvent("SearchCleared", RoutingStrategy.Bubble, 
            typeof(RoutedEventHandler), typeof(ModernSearchBar));

    public event PropertyChangedEventHandler? PropertyChanged;
    public event RoutedEventHandler SearchRequested
    {
        add { AddHandler(SearchRequestedEvent, value); }
        remove { RemoveHandler(SearchRequestedEvent, value); }
    }

    public event RoutedEventHandler SearchCleared
    {
        add { AddHandler(SearchClearedEvent, value); }
        remove { RemoveHandler(SearchClearedEvent, value); }
    }

    public string SearchQuery
    {
        get { return (string)GetValue(SearchQueryProperty); }
        set { SetValue(SearchQueryProperty, value); }
    }

    public ModernSearchBar()
    {
        InitializeComponent();
        DataContext = this;
    }

    private static void OnSearchQueryChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is ModernSearchBar control)
        {
            control.UpdateClearButtonVisibility();
            control.OnPropertyChanged(nameof(SearchQuery));
        }
    }

    private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
    {
        if (e.Key == Key.Enter)
        {
            PerformSearch();
        }
        else if (e.Key == Key.Escape)
        {
            ClearSearch();
        }
    }

    private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        // Clear placeholder when focused
        if (SearchTextBox.Background != null)
        {
            SearchTextBox.Background = System.Windows.Media.Brushes.Transparent;
        }
    }

    private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        // Restore placeholder if empty
        UpdateClearButtonVisibility();
    }

    private void ClearButton_Click(object sender, RoutedEventArgs e)
    {
        ClearSearch();
    }

    private void PerformSearch()
    {
        var args = new SearchEventArgs(SearchRequestedEvent, this)
        {
            SearchQuery = SearchQuery
        };
        RaiseEvent(args);
    }

    private void ClearSearch()
    {
        SearchQuery = string.Empty;
        SearchTextBox.Focus();
        
        var args = new RoutedEventArgs(SearchClearedEvent, this);
        RaiseEvent(args);
    }

    private void UpdateClearButtonVisibility()
    {
        ClearButton.Visibility = !string.IsNullOrEmpty(SearchQuery) ? Visibility.Visible : Visibility.Collapsed;
    }

    public void Focus()
    {
        SearchTextBox.Focus();
    }

    public void Clear()
    {
        SearchQuery = string.Empty;
    }

    protected virtual void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

public class SearchEventArgs : RoutedEventArgs
{
    public string SearchQuery { get; set; } = string.Empty;

    public SearchEventArgs(RoutedEvent routedEvent, object source) : base(routedEvent, source)
    {
    }
}
