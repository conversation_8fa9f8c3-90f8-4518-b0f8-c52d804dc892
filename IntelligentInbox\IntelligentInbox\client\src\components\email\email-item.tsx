import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { <PERSON>, Paperclip, <PERSON>, Edit } from "lucide-react";
import { cn } from "@/lib/utils";
import { apiRequest } from "@/lib/api";
import PriorityBadge from "@/components/ui/priority-badge";
import EditEmailModal from "@/components/modals/edit-email-modal";
import type { Email } from "@shared/schema";

interface EmailItemProps {
  email: Email;
  onClick?: () => void;
  isSelected?: boolean;
}

export default function EmailItem({ email, onClick, isSelected }: EmailItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const queryClient = useQueryClient();

  const markAsReadMutation = useMutation({
    mutationFn: (id: number) => apiRequest("POST", `/api/emails/${id}/read`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
    },
  });

  const toggleStarMutation = useMutation({
    mutationFn: ({ id, starred }: { id: number; starred: boolean }) => 
      apiRequest("POST", `/api/emails/${id}/star`, { starred }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/emails"] });
    },
  });

  const handleClick = () => {
    if (!email.isRead) {
      markAsReadMutation.mutate(email.id);
    }
    onClick?.();
  };

  const handleStarClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleStarMutation.mutate({ id: email.id, starred: !email.isStarred });
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditModalOpen(true);
  };

  const hasAttachments = email.attachments && Array.isArray(email.attachments) && email.attachments.length > 0;
  const hasRelatedItems = email.relatedItems && Array.isArray(email.relatedItems) && email.relatedItems.length > 0;

  return (
    <div 
      className={cn(
        "cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800",
        !email.isRead && "bg-blue-50 dark:bg-blue-900/20",
        isSelected && "bg-blue-100 dark:bg-blue-900/40 border-r-2 border-blue-500"
      )}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="px-6 py-4">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {email.sender.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center space-x-2">
                <p className={cn(
                  "text-sm font-medium",
                  email.isRead ? "text-gray-900 dark:text-white" : "text-gray-900 dark:text-white font-semibold"
                )}>
                  {email.sender}
                </p>
                <PriorityBadge priority={email.priority as "low" | "medium" | "high"} />
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {format(new Date(email.createdAt), "h:mm a")}
                </span>
                {isHovered && (
                  <button
                    onClick={handleEditClick}
                    className="p-1 rounded transition-colors text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={handleStarClick}
                  className={cn(
                    "p-1 rounded transition-colors",
                    email.isStarred 
                      ? "text-yellow-500 hover:text-yellow-600" 
                      : "text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400"
                  )}
                >
                  <Star className={cn("w-4 h-4", email.isStarred && "fill-current")} />
                </button>
              </div>
            </div>
            
            <p className={cn(
              "text-sm mb-2",
              email.isRead ? "text-gray-700 dark:text-gray-300" : "text-gray-900 dark:text-white font-medium"
            )}>
              {email.subject}
            </p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              {hasAttachments && (
                <span className="flex items-center space-x-1">
                  <Paperclip className="w-3 h-3" />
                  <span>{(email.attachments as any[]).length} attachments</span>
                </span>
              )}
              
              {hasRelatedItems && (
                <span className="flex items-center space-x-1">
                  <Link className="w-3 h-3" />
                  <span>Related items</span>
                </span>
              )}
              
              {email.aiSummary && (
                <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded-full">
                  AI Summary Available
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
      <EditEmailModal 
        email={email}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
      />
    </div>
  );
}
